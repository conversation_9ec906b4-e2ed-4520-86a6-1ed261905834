/* Dark Theme Styles */

.root {
    /* Colorful Dark Theme Variables */
    -fx-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -fx-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    -fx-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -fx-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    -fx-error: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    -fx-background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    -fx-surface: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    -fx-text: #ecf0f1;
    -fx-text-secondary: #bdc3c7;
    -fx-border: #e74c3c;

    /* Apply colorful dark background */
    -fx-background-color: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

/* Main Container Backgrounds - Colorful Dark */
.main-container {
    -fx-background-color: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    -fx-background-radius: 15px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.5), 20, 0, 0, 8);
}

.content-area {
    -fx-background-color: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(52,73,94,0.6), 12, 0, 0, 4);
}

/* Sidebar Dark Theme - Colorful */
.sidebar {
    -fx-background-color: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-background-radius: 0 15px 15px 0;
    -fx-effect: dropshadow(gaussian, rgba(44,62,80,0.7), 15, 0, 3, 0);
}

.sidebar-button {
    -fx-background-color: transparent;
    -fx-text-fill: #ecf0f1;
    -fx-font-weight: bold;
    -fx-background-radius: 25px;
    -fx-border-radius: 25px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 8, 0, 0, 3);
}

.sidebar-button:hover {
    -fx-background-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
    -fx-effect: dropshadow(gaussian, rgba(102,126,234,0.8), 12, 0, 0, 5);
}

.sidebar-button.active {
    -fx-background-color: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
    -fx-effect: dropshadow(gaussian, rgba(79,172,254,0.9), 15, 0, 0, 6);
}

/* Card Dark Theme */
.enhanced-card {
    -fx-background-color: #1E1E1E;
    -fx-border-color: #424242;
    -fx-border-width: 1px;
}

.stat-card {
    -fx-background-color: linear-gradient(135deg, #1976D2, #0D47A1);
}

/* Text Field Dark Theme */
.enhanced-textfield {
    -fx-background-color: #2C2C2C;
    -fx-border-color: #424242;
    -fx-text-fill: #FFFFFF;
    -fx-prompt-text-fill: #AAAAAA;
}

.enhanced-textfield:focused {
    -fx-border-color: #1976D2;
}

/* ComboBox Dark Theme */
.enhanced-combobox {
    -fx-background-color: #2C2C2C;
    -fx-border-color: #424242;
    -fx-text-fill: #FFFFFF;
}

.enhanced-combobox:focused {
    -fx-border-color: #1976D2;
}

.enhanced-combobox .list-cell {
    -fx-background-color: #2C2C2C;
    -fx-text-fill: #FFFFFF;
}

.enhanced-combobox .list-cell:hover {
    -fx-background-color: rgba(25,118,210,0.12);
}

/* Table Dark Theme */
.enhanced-table {
    -fx-background-color: #1E1E1E;
    -fx-border-color: #424242;
}

.enhanced-table .column-header {
    -fx-background-color: #2C2C2C;
    -fx-text-fill: #FFFFFF;
    -fx-border-color: #424242;
}

.enhanced-table .table-row-cell {
    -fx-background-color: #1E1E1E;
    -fx-text-fill: #FFFFFF;
}

.enhanced-table .table-row-cell:odd {
    -fx-background-color: #252525;
}

.enhanced-table .table-row-cell:hover {
    -fx-background-color: rgba(25,118,210,0.12);
}

.enhanced-table .table-row-cell:selected {
    -fx-background-color: rgba(25,118,210,0.2);
}

/* Button Dark Theme */
.button {
    -fx-background-color: #2C2C2C;
    -fx-border-color: #424242;
    -fx-text-fill: #FFFFFF;
}

.button:hover {
    -fx-background-color: #383838;
}

.button:pressed {
    -fx-background-color: #1E1E1E;
}

/* Primary Button */
.button.btn-primary {
    -fx-background-color: #1976D2;
    -fx-text-fill: #FFFFFF;
    -fx-border-color: transparent;
}

.button.btn-primary:hover {
    -fx-background-color: #1565C0;
}

/* Success Button */
.button.btn-success {
    -fx-background-color: #388E3C;
    -fx-text-fill: #FFFFFF;
    -fx-border-color: transparent;
}

.button.btn-success:hover {
    -fx-background-color: #2E7D32;
}

/* Warning Button */
.button.btn-warning {
    -fx-background-color: #F57C00;
    -fx-text-fill: #FFFFFF;
    -fx-border-color: transparent;
}

.button.btn-warning:hover {
    -fx-background-color: #EF6C00;
}

/* Error Button */
.button.btn-danger {
    -fx-background-color: #D32F2F;
    -fx-text-fill: #FFFFFF;
    -fx-border-color: transparent;
}

.button.btn-danger:hover {
    -fx-background-color: #C62828;
}

/* Label Dark Theme */
.label {
    -fx-text-fill: #FFFFFF;
}

.label.label-title {
    -fx-text-fill: #FFFFFF;
    -fx-font-weight: bold;
}

.label.label-subtitle {
    -fx-text-fill: #AAAAAA;
}

.label.label-secondary {
    -fx-text-fill: #AAAAAA;
}

/* Header Dark Theme */
.header-container {
    -fx-background-color: #1E1E1E;
    -fx-border-color: #424242;
    -fx-border-width: 0 0 1px 0;
}

.header-title {
    -fx-text-fill: #FFFFFF;
}

.header-subtitle {
    -fx-text-fill: #AAAAAA;
}

/* Menu Dark Theme */
.menu-bar {
    -fx-background-color: #1E1E1E;
    -fx-border-color: #424242;
}

.menu {
    -fx-text-fill: #FFFFFF;
}

.menu-item {
    -fx-background-color: #1E1E1E;
    -fx-text-fill: #FFFFFF;
}

.menu-item:hover {
    -fx-background-color: rgba(25,118,210,0.12);
}

/* Dialog Dark Theme */
.dialog-pane {
    -fx-background-color: #1E1E1E;
}

.dialog-pane .header-panel {
    -fx-background-color: #2C2C2C;
    -fx-text-fill: #FFFFFF;
}

.dialog-pane .content {
    -fx-background-color: #1E1E1E;
    -fx-text-fill: #FFFFFF;
}

/* Progress Bar Dark Theme */
.progress-bar {
    -fx-background-color: #424242;
}

.progress-bar .bar {
    -fx-background-color: #1976D2;
}

/* Separator Dark Theme */
.separator .line {
    -fx-border-color: #424242;
}

/* Tooltip Dark Theme */
.tooltip {
    -fx-background-color: rgba(66,66,66,0.95);
    -fx-text-fill: #FFFFFF;
    -fx-background-radius: 4px;
    -fx-font-size: 12px;
}

/* ScrollPane Dark Theme */
.scroll-pane {
    -fx-background-color: #1E1E1E;
}

.scroll-pane .corner {
    -fx-background-color: #1E1E1E;
}

.scroll-bar .track {
    -fx-background-color: rgba(255,255,255,0.1);
}

.scroll-bar .thumb {
    -fx-background-color: rgba(255,255,255,0.3);
}

.scroll-bar .thumb:hover {
    -fx-background-color: rgba(255,255,255,0.5);
}

/* ListView Dark Theme */
.list-view {
    -fx-background-color: #1E1E1E;
    -fx-border-color: #424242;
}

.list-cell {
    -fx-background-color: #1E1E1E;
    -fx-text-fill: #FFFFFF;
}

.list-cell:hover {
    -fx-background-color: rgba(25,118,210,0.12);
}

.list-cell:selected {
    -fx-background-color: rgba(25,118,210,0.2);
}

/* TreeView Dark Theme */
.tree-view {
    -fx-background-color: #1E1E1E;
    -fx-border-color: #424242;
}

.tree-cell {
    -fx-background-color: #1E1E1E;
    -fx-text-fill: #FFFFFF;
}

.tree-cell:hover {
    -fx-background-color: rgba(25,118,210,0.12);
}

.tree-cell:selected {
    -fx-background-color: rgba(25,118,210,0.2);
}

/* Tab Pane Dark Theme */
.tab-pane {
    -fx-background-color: #1E1E1E;
}

.tab {
    -fx-background-color: #2C2C2C;
    -fx-text-fill: #FFFFFF;
    -fx-border-color: #424242;
}

.tab:selected {
    -fx-background-color: #1E1E1E;
    -fx-text-fill: #1976D2;
}

.tab:hover {
    -fx-background-color: #383838;
}

/* Accordion Dark Theme */
.accordion {
    -fx-background-color: #1E1E1E;
}

.titled-pane {
    -fx-background-color: #1E1E1E;
    -fx-border-color: #424242;
}

.titled-pane .title {
    -fx-background-color: #2C2C2C;
    -fx-text-fill: #FFFFFF;
}

.titled-pane .content {
    -fx-background-color: #1E1E1E;
}

/* CheckBox and RadioButton Dark Theme */
.check-box {
    -fx-text-fill: #FFFFFF;
}

.radio-button {
    -fx-text-fill: #FFFFFF;
}

/* Slider Dark Theme */
.slider {
    -fx-background-color: #424242;
}

.slider .track {
    -fx-background-color: #424242;
}

.slider .thumb {
    -fx-background-color: #1976D2;
}

/* Spinner Dark Theme */
.spinner {
    -fx-background-color: #2C2C2C;
    -fx-border-color: #424242;
}

.spinner .text-field {
    -fx-background-color: #2C2C2C;
    -fx-text-fill: #FFFFFF;
}

/* DatePicker Dark Theme */
.date-picker {
    -fx-background-color: #2C2C2C;
    -fx-border-color: #424242;
}

.date-picker .text-field {
    -fx-background-color: #2C2C2C;
    -fx-text-fill: #FFFFFF;
}
