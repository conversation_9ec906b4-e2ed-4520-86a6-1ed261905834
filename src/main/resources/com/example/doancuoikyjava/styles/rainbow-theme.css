/* Ultra Vibrant Rainbow Theme Styles */

.root {
    /* Rainbow Theme Variables */
    -fx-primary: #FF6B6B;
    -fx-secondary: #4ECDC4;
    -fx-success: #45B7D1;
    -fx-warning: #FFEAA7;
    -fx-error: #FF69B4;
    -fx-background: #FF6B6B;
    -fx-surface: #FFFFFF;
    -fx-text: #2C3E50;
    -fx-text-secondary: #7F8C8D;
    -fx-border: #E74C3C;
    
    /* Apply ultra vibrant rainbow background */
    -fx-background-color: conic-gradient(from 0deg, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7, #FF69B4, #FF6B6B);
}

/* Main Container Backgrounds - Rainbow Magic */
.main-container {
    -fx-background-color: conic-gradient(from 45deg at center, #FF6B6B 0%, #4ECDC4 16.66%, #45B7D1 33.33%, #96CEB4 50%, #FFEAA7 66.66%, #FF69B4 83.33%, #FF6B6B 100%);
    -fx-background-radius: 30px;
    -fx-effect: dropshadow(gaussian, rgba(255,107,107,0.8), 25, 0, 0, 10);
}

.content-area {
    -fx-background-color: radial-gradient(circle at center, #FFFFFF 0%, #F8F9FF 30%, #E8F4FD 60%, #FFF0F5 100%);
    -fx-background-radius: 25px;
    -fx-effect: dropshadow(gaussian, rgba(255,255,255,0.9), 20, 0, 0, 8);
}

/* Sidebar Rainbow Theme */
.sidebar {
    -fx-background-color: conic-gradient(from 90deg at center, #FF6B6B 0%, #4ECDC4 25%, #45B7D1 50%, #96CEB4 75%, #FF6B6B 100%);
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-background-radius: 0 30px 30px 0;
    -fx-effect: dropshadow(gaussian, rgba(255,107,107,0.9), 25, 0, 8, 0);
}

.sidebar-button {
    -fx-background-color: rgba(255,255,255,0.3);
    -fx-text-fill: #FFFFFF;
    -fx-font-weight: bold;
    -fx-background-radius: 35px;
    -fx-border-radius: 35px;
    -fx-effect: dropshadow(gaussian, rgba(255,255,255,0.5), 12, 0, 0, 4);
    -fx-border-color: rgba(255,255,255,0.5);
    -fx-border-width: 3px;
    -fx-font-size: 16px;
    -fx-padding: 15px 25px;
}

.sidebar-button:hover {
    -fx-background-color: radial-gradient(circle at center, #FFFFFF 0%, #FF69B4 100%);
    -fx-text-fill: #2C3E50;
    -fx-scale-x: 1.2;
    -fx-scale-y: 1.2;
    -fx-effect: dropshadow(gaussian, rgba(255,105,180,1.0), 25, 0, 0, 8);
    -fx-border-color: #FFFFFF;
    -fx-border-width: 4px;
}

.sidebar-button.active {
    -fx-background-color: radial-gradient(circle at center, #FFEAA7 0%, #FDCB6E 100%);
    -fx-text-fill: #2C3E50;
    -fx-font-weight: bold;
    -fx-effect: dropshadow(gaussian, rgba(255,234,167,1.0), 30, 0, 0, 10);
    -fx-border-color: #E17055;
    -fx-border-width: 4px;
}

/* Card Rainbow Theme */
.enhanced-card {
    -fx-background-color: radial-gradient(circle at center, #FFFFFF 0%, #F8F9FF 40%, #E8F4FD 80%, #FFF0F5 100%);
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-background-radius: 25px;
    -fx-effect: dropshadow(gaussian, rgba(255,107,107,0.4), 15, 0, 0, 8);
}

.enhanced-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(255,107,107,0.8), 25, 0, 0, 12);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
    -fx-background-color: radial-gradient(circle at center, #FFFFFF 0%, #FFE4E1 50%, #FFF0F5 100%);
}

.stat-card {
    -fx-background-color: conic-gradient(from 0deg at center, #FF6B6B 0%, #4ECDC4 33%, #45B7D1 66%, #FF6B6B 100%);
    -fx-background-radius: 25px;
    -fx-effect: dropshadow(gaussian, rgba(255,107,107,0.8), 20, 0, 0, 8);
}

.stat-card:hover {
    -fx-background-color: conic-gradient(from 45deg at center, #FF69B4 0%, #FFEAA7 50%, #4ECDC4 100%);
    -fx-effect: dropshadow(gaussian, rgba(255,105,180,1.0), 30, 0, 0, 12);
    -fx-scale-x: 1.1;
    -fx-scale-y: 1.1;
}

/* Button Rainbow Theme */
.button {
    -fx-background-color: radial-gradient(circle at center, #FFFFFF 0%, #F0F8FF 100%);
    -fx-border-color: #FF6B6B;
    -fx-border-width: 4px;
    -fx-text-fill: #2C3E50;
    -fx-background-radius: 35px;
    -fx-border-radius: 35px;
    -fx-font-weight: bold;
    -fx-font-size: 16px;
    -fx-padding: 18px 30px;
    -fx-effect: dropshadow(gaussian, rgba(255,107,107,0.6), 15, 0, 0, 6);
}

.button:hover {
    -fx-background-color: radial-gradient(circle at center, #FF6B6B 0%, #4ECDC4 100%);
    -fx-text-fill: #FFFFFF;
    -fx-scale-x: 1.15;
    -fx-scale-y: 1.15;
    -fx-effect: dropshadow(gaussian, rgba(255,107,107,1.0), 25, 0, 0, 10);
    -fx-border-color: #FFFFFF;
    -fx-border-width: 5px;
}

.button.btn-primary {
    -fx-background-color: radial-gradient(circle at center, #FF6B6B 0%, #FF8E8E 100%);
    -fx-text-fill: #FFFFFF;
    -fx-border-color: #FF4757;
    -fx-effect: dropshadow(gaussian, rgba(255,107,107,0.8), 18, 0, 0, 7);
}

.button.btn-primary:hover {
    -fx-background-color: conic-gradient(from 0deg at center, #FF69B4 0%, #FF1493 50%, #DC143C 100%);
    -fx-effect: dropshadow(gaussian, rgba(255,105,180,1.0), 30, 0, 0, 12);
}

.button.btn-success {
    -fx-background-color: radial-gradient(circle at center, #4ECDC4 0%, #26D0CE 100%);
    -fx-text-fill: #FFFFFF;
    -fx-border-color: #00CEC9;
    -fx-effect: dropshadow(gaussian, rgba(78,205,196,0.8), 18, 0, 0, 7);
}

.button.btn-success:hover {
    -fx-background-color: conic-gradient(from 120deg at center, #00B894 0%, #00CEC9 50%, #74B9FF 100%);
    -fx-effect: dropshadow(gaussian, rgba(0,206,201,1.0), 30, 0, 0, 12);
}

.button.btn-warning {
    -fx-background-color: radial-gradient(circle at center, #FFEAA7 0%, #FDCB6E 100%);
    -fx-text-fill: #2C3E50;
    -fx-border-color: #E17055;
    -fx-effect: dropshadow(gaussian, rgba(255,234,167,0.8), 18, 0, 0, 7);
}

.button.btn-warning:hover {
    -fx-background-color: conic-gradient(from 240deg at center, #FFA07A 0%, #FF7F50 50%, #FF6347 100%);
    -fx-text-fill: #FFFFFF;
    -fx-effect: dropshadow(gaussian, rgba(255,160,122,1.0), 30, 0, 0, 12);
}

/* Text and Labels */
.label {
    -fx-text-fill: #2C3E50;
    -fx-font-weight: bold;
}

.label.label-title {
    -fx-text-fill: #2C3E50;
    -fx-font-weight: bold;
    -fx-font-size: 24px;
    -fx-effect: dropshadow(gaussian, rgba(255,107,107,0.5), 8, 0, 0, 3);
}

.label.label-subtitle {
    -fx-text-fill: #7F8C8D;
    -fx-font-size: 18px;
}

/* Header Rainbow Theme */
.header-container {
    -fx-background-color: conic-gradient(from 0deg at center, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-background-radius: 25px 25px 0 0;
    -fx-effect: dropshadow(gaussian, rgba(255,107,107,0.6), 15, 0, 0, 5);
}

.header-title {
    -fx-text-fill: #FFFFFF;
    -fx-font-weight: bold;
    -fx-font-size: 28px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.5), 5, 0, 0, 2);
}

.header-subtitle {
    -fx-text-fill: rgba(255,255,255,0.9);
    -fx-font-size: 16px;
}

/* Text Fields Rainbow Theme */
.enhanced-textfield {
    -fx-background-color: radial-gradient(circle at center, #FFFFFF 0%, #F0F8FF 100%);
    -fx-border-color: #4ECDC4;
    -fx-text-fill: #2C3E50;
    -fx-prompt-text-fill: #7F8C8D;
    -fx-background-radius: 25px;
    -fx-border-radius: 25px;
    -fx-border-width: 3px;
    -fx-font-size: 16px;
    -fx-padding: 15px 20px;
    -fx-effect: dropshadow(gaussian, rgba(78,205,196,0.5), 12, 0, 0, 5);
}

.enhanced-textfield:focused {
    -fx-border-color: #FF6B6B;
    -fx-effect: dropshadow(gaussian, rgba(255,107,107,0.8), 18, 0, 0, 8);
    -fx-background-color: radial-gradient(circle at center, #FFFFFF 0%, #FFE4E1 100%);
}

/* Table Rainbow Theme */
.enhanced-table {
    -fx-background-color: radial-gradient(circle at center, #FFFFFF 0%, #F8F9FF 100%);
    -fx-border-color: #FF6B6B;
    -fx-background-radius: 20px;
    -fx-border-radius: 20px;
    -fx-border-width: 3px;
    -fx-effect: dropshadow(gaussian, rgba(255,107,107,0.4), 12, 0, 0, 5);
}

.enhanced-table .column-header {
    -fx-background-color: conic-gradient(from 0deg at center, #FF6B6B 0%, #4ECDC4 100%);
    -fx-text-fill: #FFFFFF;
    -fx-border-color: #FF4757;
    -fx-font-weight: bold;
    -fx-font-size: 16px;
}

.enhanced-table .table-row-cell {
    -fx-background-color: radial-gradient(circle at center, #FFFFFF 0%, #F8F9FF 100%);
    -fx-text-fill: #2C3E50;
}

.enhanced-table .table-row-cell:hover {
    -fx-background-color: rgba(255,107,107,0.2);
}

.enhanced-table .table-row-cell:selected {
    -fx-background-color: rgba(255,107,107,0.4);
}

/* Progress Bar Rainbow Theme */
.progress-bar {
    -fx-background-color: rgba(255,255,255,0.5);
    -fx-background-radius: 20px;
    -fx-border-color: #FF6B6B;
    -fx-border-width: 2px;
    -fx-border-radius: 20px;
}

.progress-bar .bar {
    -fx-background-color: conic-gradient(from 0deg at center, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
    -fx-background-radius: 18px;
}

/* Scrollbar Rainbow Theme */
.scroll-bar .track {
    -fx-background-color: rgba(255,107,107,0.2);
    -fx-background-radius: 15px;
}

.scroll-bar .thumb {
    -fx-background-color: radial-gradient(circle at center, #FF6B6B 0%, #4ECDC4 100%);
    -fx-background-radius: 15px;
}

.scroll-bar .thumb:hover {
    -fx-background-color: radial-gradient(circle at center, #FF69B4 0%, #45B7D1 100%);
}

/* Special Rainbow Effects */
.rainbow-text {
    -fx-fill: conic-gradient(from 0deg, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7, #FF69B4);
    -fx-font-weight: bold;
    -fx-font-size: 20px;
    -fx-effect: dropshadow(gaussian, rgba(255,107,107,0.8), 10, 0, 0, 3);
}

.rainbow-border {
    -fx-border-width: 5px;
    -fx-border-color: conic-gradient(from 0deg, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7, #FF69B4);
    -fx-border-radius: 25px;
}

.rainbow-glow {
    -fx-effect: dropshadow(gaussian, #FF6B6B, 20, 0, 0, 0), 
                dropshadow(gaussian, #4ECDC4, 20, 0, 0, 0),
                dropshadow(gaussian, #45B7D1, 20, 0, 0, 0);
}
