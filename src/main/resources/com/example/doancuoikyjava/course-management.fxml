<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.CourseManagementController">
   <top>
      <HBox alignment="CENTER_LEFT" styleClass="nav-bar" spacing="20.0">
         <children>
            <Button fx:id="backButton" onAction="#goBack" styleClass="nav-button" text="← Quay lại" />
            <Label styleClass="nav-button" text="📚 QUẢN LÝ MÔN HỌC">
               <font>
                  <Font name="System Bold" size="16.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="welcomeLabel" styleClass="nav-button" text="Xin chào, Admin">
               <font>
                  <Font size="14.0" />
               </font>
            </Label>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>
   </top>
   <center>
      <VBox styleClass="content-container" spacing="20.0">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label styleClass="label-title" text="Danh sách môn học">
                     <font>
                        <Font name="System Bold" size="24.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="addCourseBtn" onAction="#showAddCourseDialog" styleClass="btn,btn-primary" text="+ Thêm môn học" />
                  <Button fx:id="refreshBtn" onAction="#refreshData" styleClass="btn,btn-secondary" text="🔄 Làm mới" />
               </children>
            </HBox>
            
            <HBox spacing="15.0">
               <children>
                  <TextField fx:id="searchField" promptText="Tìm kiếm theo tên, mã môn học..." HBox.hgrow="ALWAYS" />
                  <ComboBox fx:id="teacherFilterComboBox" promptText="Lọc theo giáo viên" />
                  <ComboBox fx:id="creditsFilterComboBox" promptText="Lọc theo tín chỉ" />
                  <Button fx:id="searchBtn" onAction="#searchCourses" styleClass="btn,btn-warning" text="Tìm kiếm" />
               </children>
            </HBox>
            
            <TableView fx:id="coursesTableView" VBox.vgrow="ALWAYS">
               <columns>
                  <TableColumn fx:id="courseIdColumn" prefWidth="100.0" text="Mã MH" />
                  <TableColumn fx:id="courseNameColumn" prefWidth="250.0" text="Tên môn học" />
                  <TableColumn fx:id="creditsColumn" prefWidth="80.0" text="Tín chỉ" />
                  <TableColumn fx:id="teacherNameColumn" prefWidth="200.0" text="Giáo viên" />
                  <TableColumn fx:id="scheduleColumn" prefWidth="150.0" text="Giờ học" />
                  <TableColumn fx:id="classroomColumn" prefWidth="100.0" text="Phòng học" />
                  <TableColumn fx:id="enrolledColumn" prefWidth="100.0" text="SV đăng ký" />
                  <TableColumn fx:id="maxStudentsColumn" prefWidth="100.0" text="Sĩ số tối đa" />
                  <TableColumn fx:id="actionsColumn" prefWidth="150.0" text="Thao tác" />
               </columns>
            </TableView>
            
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label fx:id="totalCoursesLabel" text="Tổng số môn học: 0" />
                  <Region HBox.hgrow="ALWAYS" />

                  <Button fx:id="deleteSelectedBtn" onAction="#deleteSelectedCourses" styleClass="btn,btn-danger" text="🗑 Xóa đã chọn" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </VBox>
   </center>
</BorderPane>
