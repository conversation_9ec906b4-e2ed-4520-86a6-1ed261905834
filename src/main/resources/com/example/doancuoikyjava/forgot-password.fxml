<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" 
      fx:controller="com.example.doancuoikyjava.controller.ForgotPasswordController"
      spacing="20" style="-fx-background-color: white; -fx-padding: 30;">
   
   <!-- Header -->
   <VBox alignment="CENTER" spacing="10">
      <children>
         <Label text="🔑" style="-fx-font-size: 48px;" />
         <Label text="QUÊN MẬT KHẨU" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
         <Label text="Nhập email hoặc tên đăng nhập để nhận mật khẩu mới" 
                style="-fx-font-size: 14px; -fx-text-fill: #7f8c8d; -fx-text-alignment: center;" 
                wrapText="true" maxWidth="350" />
      </children>
   </VBox>

   <!-- Input Form -->
   <VBox spacing="15" style="-fx-max-width: 350;">
      <children>
         <!-- Email/Username Field -->
         <VBox spacing="8">
            <children>
               <Label text="📧 Email hoặc Tên đăng nhập" 
                      style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #34495e;" />
               <TextField fx:id="emailOrUsernameField" 
                         promptText="Nhập email hoặc tên đăng nhập"
                         style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 8; 
                                -fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-pref-height: 45;" />
            </children>
         </VBox>

         <!-- Status Label -->
         <Label fx:id="statusLabel" visible="false" wrapText="true" maxWidth="350"
                style="-fx-font-size: 12px; -fx-background-radius: 5; -fx-padding: 8;" />

         <!-- Progress Indicator -->
         <HBox alignment="CENTER">
            <children>
               <ProgressIndicator fx:id="progressIndicator" visible="false" 
                                 style="-fx-progress-color: #667eea;" maxWidth="30" maxHeight="30" />
            </children>
         </HBox>
      </children>
   </VBox>

   <!-- Buttons -->
   <HBox spacing="15" alignment="CENTER" style="-fx-max-width: 350;">
      <children>
         <Button fx:id="sendButton" onAction="#handleSendNewPassword" 
                 text="📤 GỬI MẬT KHẨU MỚI" 
                 style="-fx-background-color: linear-gradient(to right, #667eea, #764ba2); 
                        -fx-text-fill: white; -fx-font-size: 14px; -fx-font-weight: bold; 
                        -fx-background-radius: 8; -fx-padding: 12 20; -fx-cursor: hand;"
                 HBox.hgrow="ALWAYS" maxWidth="Infinity" />
         
         <Button fx:id="cancelButton" onAction="#handleCancel" 
                 text="❌ HỦY" 
                 style="-fx-background-color: #95a5a6; -fx-text-fill: white; 
                        -fx-font-size: 14px; -fx-font-weight: bold; 
                        -fx-background-radius: 8; -fx-padding: 12 20; -fx-cursor: hand;"
                 HBox.hgrow="ALWAYS" maxWidth="Infinity" />
      </children>
   </HBox>

   <!-- Instructions -->
   <VBox spacing="10" style="-fx-max-width: 350; -fx-background-color: rgba(52, 152, 219, 0.1); 
                             -fx-background-radius: 8; -fx-padding: 15;">
      <children>
         <Label text="💡 Hướng dẫn:" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
         <VBox spacing="5">
            <children>
               <Label text="• Nhập email hoặc tên đăng nhập của bạn" 
                      style="-fx-font-size: 12px; -fx-text-fill: #34495e;" />
               <Label text="• Hệ thống sẽ tạo mật khẩu mới và gửi qua email" 
                      style="-fx-font-size: 12px; -fx-text-fill: #34495e;" />
               <Label text="• Kiểm tra hộp thư (kể cả thư rác)" 
                      style="-fx-font-size: 12px; -fx-text-fill: #34495e;" />
               <Label text="• Đăng nhập và đổi mật khẩu ngay sau đó" 
                      style="-fx-font-size: 12px; -fx-text-fill: #34495e;" />
            </children>
         </VBox>
      </children>
   </VBox>

</VBox>
