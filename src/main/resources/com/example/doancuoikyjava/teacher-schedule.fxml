<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.TeacherScheduleController">
   <top>
      <HBox alignment="CENTER_LEFT" styleClass="nav-bar" spacing="20.0">
         <children>
            <Button fx:id="backButton" onAction="#goBack" styleClass="nav-button" text="← Quay lại" />
            <Label styleClass="nav-button" text="📅 LỊCH GIẢNG DẠY">
               <font>
                  <Font name="System Bold" size="16.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="welcomeLabel" styleClass="nav-button" text="Xin chào, Giáo viên">
               <font>
                  <Font size="14.0" />
               </font>
            </Label>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>
   </top>
   <center>
      <VBox styleClass="content-container" spacing="20.0">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label styleClass="label-title" text="Lịch giảng dạy">
                     <font>
                        <Font name="System Bold" size="24.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="refreshBtn" onAction="#refreshData" styleClass="btn,btn-secondary" text="🔄 Làm mới" />
               </children>
            </HBox>
            
            <!-- Thống kê nhanh -->
            <HBox spacing="20.0">
               <children>
                  <VBox styleClass="stats-card">
                     <children>
                        <Label fx:id="totalCoursesLabel" styleClass="stats-number" text="0">
                           <font>
                              <Font name="System Bold" size="36.0" />
                           </font>
                        </Label>
                        <Label styleClass="stats-label" text="MÔN HỌC">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Label>
                     </children>
                  </VBox>
                  
                  <VBox styleClass="stats-card">
                     <children>
                        <Label fx:id="totalClassesLabel" styleClass="stats-number" text="0">
                           <font>
                              <Font name="System Bold" size="36.0" />
                           </font>
                        </Label>
                        <Label styleClass="stats-label" text="TIẾT/TUẦN">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Label>
                     </children>
                  </VBox>
                  
                  <VBox styleClass="stats-card">
                     <children>
                        <Label fx:id="totalStudentsLabel" styleClass="stats-number" text="0">
                           <font>
                              <Font name="System Bold" size="36.0" />
                           </font>
                        </Label>
                        <Label styleClass="stats-label" text="SINH VIÊN">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Label>
                     </children>
                  </VBox>
               </children>
            </HBox>
            
            <!-- Lịch học theo tuần -->
            <VBox styleClass="card" spacing="15.0">
               <children>
                  <Label styleClass="card-header" text="Lịch tuần này">
                     <font>
                        <Font name="System Bold" size="18.0" />
                     </font>
                  </Label>
                  
                  <TableView fx:id="scheduleTableView" prefHeight="300.0">
                     <columns>
                        <TableColumn fx:id="dayColumn" prefWidth="100.0" text="Thứ" />
                        <TableColumn fx:id="timeColumn" prefWidth="120.0" text="Giờ học" />
                        <TableColumn fx:id="courseColumn" prefWidth="200.0" text="Môn học" />
                        <TableColumn fx:id="classroomColumn" prefWidth="100.0" text="Phòng" />
                        <TableColumn fx:id="studentsCountColumn" prefWidth="80.0" text="SV" />
                        <TableColumn fx:id="creditsColumn" prefWidth="80.0" text="Tín chỉ" />
                        <TableColumn fx:id="statusColumn" prefWidth="100.0" text="Trạng thái" />
                        <TableColumn fx:id="actionsColumn" prefWidth="120.0" text="Thao tác" />
                     </columns>
                  </TableView>
               </children>
            </HBox>
            
            <!-- Lịch hôm nay và tuần tới -->
            <HBox spacing="20.0">
               <children>
                  <VBox styleClass="card" spacing="15.0" HBox.hgrow="ALWAYS">
                     <children>
                        <Label styleClass="card-header" text="Lịch hôm nay">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        <ListView fx:id="todayScheduleListView" prefHeight="200.0" />
                     </children>
                  </VBox>
                  
                  <VBox styleClass="card" spacing="15.0" HBox.hgrow="ALWAYS">
                     <children>
                        <Label styleClass="card-header" text="Lịch tuần tới">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        <ListView fx:id="nextWeekScheduleListView" prefHeight="200.0" />
                     </children>
                  </VBox>
               </children>
            </HBox>
            
            <!-- Ghi chú và thông báo -->
            <VBox styleClass="card" spacing="15.0">
               <children>
                  <Label styleClass="card-header" text="Ghi chú và nhắc nhở">
                     <font>
                        <Font name="System Bold" size="18.0" />
                     </font>
                  </Label>
                  <TextArea fx:id="notesTextArea" prefHeight="100.0" promptText="Nhập ghi chú về lịch giảng dạy..." />
                  <HBox spacing="10.0">
                     <children>
                        <Button fx:id="saveNotesBtn" onAction="#saveNotes" styleClass="btn,btn-primary" text="💾 Lưu ghi chú" />
                        <Button fx:id="clearNotesBtn" onAction="#clearNotes" styleClass="btn,btn-secondary" text="🗑 Xóa ghi chú" />
                     </children>
                  </HBox>
               </children>
            </HBox>
            
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label fx:id="currentTimeLabel" text="Thời gian hiện tại: " />
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="exportBtn" onAction="#exportSchedule" styleClass="btn,btn-success" text="📊 Xuất lịch dạy" />
                  <Button fx:id="printBtn" onAction="#printSchedule" styleClass="btn,btn-warning" text="🖨 In lịch dạy" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </VBox>
   </center>
</BorderPane>
