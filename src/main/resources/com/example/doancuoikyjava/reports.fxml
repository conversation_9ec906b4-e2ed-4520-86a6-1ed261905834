<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.ReportController">
   <top>
      <VBox styleClass="header-section">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-background-color: linear-gradient(to right, #667eea, #764ba2); -fx-padding: 20;">
               <children>
                  <Button fx:id="backButton" onAction="#goBack" styleClass="back-button" text="← Quay lại"
                          style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-border-color: white; -fx-border-radius: 5; -fx-background-radius: 5; -fx-font-weight: bold;" />
                  <Label fx:id="welcomeLabel" styleClass="page-title" text="📋 Báo cáo"
                         style="-fx-text-fill: white; -fx-font-size: 24px; -fx-font-weight: bold;" />
               </children>
            </HBox>
         </children>
      </VBox>
   </top>
   <center>
      <VBox spacing="20.0">
         <children>
            <!-- Control Panel -->
            <VBox spacing="20.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);">
               <children>
                  <Label text="🔧 Tùy chọn báo cáo" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />

                  <GridPane hgap="20.0" vgap="15.0">
                     <columnConstraints>
                        <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                        <ColumnConstraints hgrow="ALWAYS" />
                     </columnConstraints>
                     <children>
                        <Label text="Loại báo cáo:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <ComboBox fx:id="reportTypeComboBox" prefWidth="350.0" promptText="Chọn loại báo cáo..."
                                  style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-background-radius: 8;"
                                  GridPane.columnIndex="1" GridPane.rowIndex="0" />

                        <Label text="Sinh viên:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <ComboBox fx:id="studentComboBox" prefWidth="350.0" promptText="Chọn sinh viên..." visible="false"
                                  style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-background-radius: 8;"
                                  GridPane.columnIndex="1" GridPane.rowIndex="1" />

                        <Label text="Giáo viên:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <ComboBox fx:id="teacherComboBox" prefWidth="350.0" promptText="Chọn giáo viên..." visible="false"
                                  style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-background-radius: 8;"
                                  GridPane.columnIndex="1" GridPane.rowIndex="2" />
                     </children>
                  </GridPane>

                  <HBox alignment="CENTER_LEFT" spacing="15.0">
                     <children>
                        <Button fx:id="generateReportButton" onAction="#generateReport" text="📊 Tạo báo cáo"
                                style="-fx-background-color: linear-gradient(to bottom, #3498db, #2980b9); -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 8; -fx-padding: 10 20;" />
                        <Button onAction="#exportReport" text="📤 Xuất báo cáo"
                                style="-fx-background-color: linear-gradient(to bottom, #27ae60, #229954); -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 8; -fx-padding: 10 20;" />
                        <Button onAction="#clearReport" text="🗑️ Xóa"
                                style="-fx-background-color: linear-gradient(to bottom, #e74c3c, #c0392b); -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 8; -fx-padding: 10 20;" />
                        <Button onAction="#refreshData" text="🔄 Làm mới"
                                style="-fx-background-color: linear-gradient(to bottom, #f39c12, #e67e22); -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 8; -fx-padding: 10 20;" />
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
               </padding>
            </VBox>
            
            <!-- Report Display -->
            <VBox spacing="15.0" VBox.vgrow="ALWAYS" style="-fx-background-color: white; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);">
               <children>
                  <HBox alignment="CENTER_LEFT" spacing="10.0">
                     <children>
                        <Label text="📋 Nội dung báo cáo" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                        <Region HBox.hgrow="ALWAYS" />
                        <Label text="💡 Tip: Sử dụng 'Xuất báo cáo' để xem toàn màn hình" style="-fx-font-size: 12px; -fx-text-fill: #7f8c8d; -fx-font-style: italic;" />
                     </children>
                  </HBox>

                  <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS" style="-fx-background-color: transparent; -fx-border-color: transparent;">
                     <content>
                        <TextArea fx:id="reportTextArea" prefHeight="450.0"
                                  promptText="📊 Báo cáo sẽ được hiển thị ở đây...&#10;&#10;🔹 Chọn loại báo cáo từ dropdown phía trên&#10;🔹 Chọn đối tượng cụ thể (nếu cần)&#10;🔹 Nhấn 'Tạo báo cáo' để bắt đầu&#10;🔹 Sử dụng 'Xuất báo cáo' để xem chi tiết đầy đủ"
                                  wrapText="true"
                                  style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-background-radius: 8; -fx-font-family: 'Consolas', 'Monaco', monospace; -fx-font-size: 13px;" />
                     </content>
                  </ScrollPane>
               </children>
               <padding>
                  <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
               </padding>
            </VBox>
         </children>
         <padding>
            <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
         </padding>
         <style>
            -fx-background-color: linear-gradient(to bottom, #f5f7fa, #c3cfe2);
         </style>
      </VBox>
   </center>
</BorderPane>
