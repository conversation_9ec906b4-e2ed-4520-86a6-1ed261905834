<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.StudentManagementController">
   <top>
      <HBox alignment="CENTER_LEFT" styleClass="nav-bar" spacing="20.0">
         <children>
            <Button fx:id="backButton" onAction="#goBack" styleClass="nav-button" text="← Quay lại" />
            <Label styleClass="nav-button" text="👨‍🎓 QUẢN LÝ SINH VIÊN">
               <font>
                  <Font name="System Bold" size="16.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="welcomeLabel" styleClass="nav-button" text="Xin chào, Admin">
               <font>
                  <Font size="14.0" />
               </font>
            </Label>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>
   </top>
   <center>
      <VBox styleClass="content-container" spacing="20.0">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label styleClass="label-title" text="Danh sách sinh viên">
                     <font>
                        <Font name="System Bold" size="24.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="addStudentBtn" onAction="#showAddStudentDialog" styleClass="btn,btn-primary" text="+ Thêm sinh viên" />
                  <Button fx:id="refreshBtn" onAction="#refreshData" styleClass="btn,btn-secondary" text="🔄 Làm mới" />
               </children>
            </HBox>
            
            <HBox spacing="15.0">
               <children>
                  <TextField fx:id="searchField" promptText="Tìm kiếm theo tên, MSSV..." HBox.hgrow="ALWAYS" />
                  <ComboBox fx:id="classFilterComboBox" promptText="Lọc theo lớp" />
                  <ComboBox fx:id="majorFilterComboBox" promptText="Lọc theo ngành" />
                  <Button fx:id="searchBtn" onAction="#searchStudents" styleClass="btn,btn-warning" text="Tìm kiếm" />
               </children>
            </HBox>
            
            <TableView fx:id="studentsTableView" VBox.vgrow="ALWAYS">
               <columns>
                  <TableColumn fx:id="studentIdColumn" prefWidth="100.0" text="MSSV" />
                  <TableColumn fx:id="fullNameColumn" prefWidth="200.0" text="Họ và tên" />
                  <TableColumn fx:id="classNameColumn" prefWidth="100.0" text="Lớp" />
                  <TableColumn fx:id="majorColumn" prefWidth="150.0" text="Ngành" />
                  <TableColumn fx:id="yearColumn" prefWidth="80.0" text="Năm" />
                  <TableColumn fx:id="gpaColumn" prefWidth="80.0" text="GPA" />
                  <TableColumn fx:id="emailColumn" prefWidth="200.0" text="Email" />
                  <TableColumn fx:id="phoneColumn" prefWidth="120.0" text="Điện thoại" />
                  <TableColumn fx:id="actionsColumn" prefWidth="150.0" text="Thao tác" />
               </columns>
            </TableView>
            
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label fx:id="totalStudentsLabel" text="Tổng số sinh viên: 0" />
                  <Region HBox.hgrow="ALWAYS" />

                  <Button fx:id="deleteSelectedBtn" onAction="#deleteSelectedStudents" styleClass="btn,btn-danger" text="🗑 Xóa đã chọn" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </VBox>
   </center>
</BorderPane>
