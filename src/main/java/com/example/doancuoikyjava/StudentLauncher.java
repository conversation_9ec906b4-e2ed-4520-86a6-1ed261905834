// IMPORTANT: If you see "JavaFX runtime components are missing", you must add VM options:
// --module-path "path\to\javafx-sdk-XX\lib" --add-modules javafx.controls,javafx.fxml
// Replace "path\to\javafx-sdk-XX\lib" with the path to your JavaFX SDK lib folder.
// In IntelliJ: Run > Edit Configurations > VM options
// In command line: java --module-path ... --add-modules ...
package com.example.doancuoikyjava;

import com.example.doancuoikyjava.model.Student;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.UserService;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.application.Application;
import javafx.stage.Stage;

import java.time.LocalDate;

/**
 * Student Launcher - Direct access to Student Dashboard
 */
public class StudentLauncher extends Application {

    private UserService userService;

    @Override
    public void start(Stage primaryStage) {
        try {
            System.out.println("🔄 Đang khởi tạo UserService cho sinh viên...");
            this.userService = new UserService();
            System.out.println("✅ UserService đã được khởi tạo thành công!");

            // Initialize default student if not exists
            System.out.println("🔍 Đang kiểm tra tài khoản Sinh viên mặc định...");
            initializeDefaultStudent();

            // Get student user
            System.out.println("🔐 Đang xác thực tài khoản Sinh viên...");
            User student = userService.getUserById("SV001");
            if (student == null) {
                System.err.println("❌ ════════════════════════════════════════");
                System.err.println("❌ KHÔNG THỂ TÌM THẤY HOẶC TẠO TÀI KHOẢN SINH VIÊN!");
                System.err.println("❌ ════════════════════════════════════════");
                return;
            }

            System.out.println("✅ Xác thực thành công!");
            System.out.println("🎯 Đang thiết lập SceneManager cho sinh viên...");

            // Set up SceneManager
            SceneManager.setPrimaryStage(primaryStage);
            SceneManager.setCurrentUser(student);

            System.out.println("🚀 Đang khởi động Student Dashboard...");

            // Launch student dashboard directly
            SceneManager.switchScene("/com/example/doancuoikyjava/student-dashboard.fxml",
                                   "🎓 STUDENT DASHBOARD - Hệ thống quản lý học tập");

            System.out.println("🎉 ════════════════════════════════════════");
            System.out.println("🎉 STUDENT DASHBOARD ĐÃ KHỞI ĐỘNG THÀNH CÔNG!");
            System.out.println("🎉 ════════════════════════════════════════");
            System.out.println("👤 Đăng nhập với tư cách: " + student.getFullName());
            System.out.println("🎓 Ngành học: Khoa học máy tính");
            System.out.println("📚 Có thể học tập và theo dõi tiến độ");
            System.out.println("🎯 Sẵn sàng sử dụng các chức năng học tập!");

        } catch (Exception e) {
            System.err.println("💥 ════════════════════════════════════════");
            System.err.println("💥 LỖI KHỞI ĐỘNG STUDENT DASHBOARD!");
            System.err.println("💥 ════════════════════════════════════════");
            System.err.println("❌ Chi tiết lỗi: " + e.getMessage());
            System.err.println("🔧 Vui lòng kiểm tra cấu hình và thử lại!");
            e.printStackTrace();
        }
    }

    private void initializeDefaultStudent() {
        try {
            if (userService.getUserById("SV001") == null) {
                System.out.println("🆕 Tài khoản Sinh viên chưa tồn tại, đang tạo mới...");

                Student student = new Student();
                student.setUserId("SV001");
                student.setUsername("student");
                student.setPassword("student123");
                student.setFullName("Nguyễn Văn Học");
                student.setEmail("<EMAIL>");
                student.setPhone("0123987654");
                student.setRole(User.UserRole.STUDENT);
                student.setStudentId("SV001");
                student.setMajor("Khoa học máy tính");
                student.setYear(2024);
                student.setDateOfBirth(LocalDate.of(2002, 5, 15));
                student.setAddress("123 Đường ABC, Quận 1, TP.HCM");
                student.setGpa(3.5);

                userService.addUser(student);
                System.out.println("🎉 ════════════════════════════════════════");
                System.out.println("🎉 TẠO TÀI KHOẢN SINH VIÊN THÀNH CÔNG!");
                System.out.println("🎉 ════════════════════════════════════════");
                System.out.println("👤 Tên đăng nhập: student");
                System.out.println("🔐 Mật khẩu: student123");
                System.out.println("🎓 Họ tên: Nguyễn Văn Học");
                System.out.println("📧 Email: <EMAIL>");
                System.out.println("📱 Điện thoại: 0123987654");
                System.out.println("🏫 Ngành: Khoa học máy tính");
                System.out.println("📅 Năm học: 2024");
                System.out.println("🎂 Ngày sinh: 15/05/2002");
                System.out.println("🏠 Địa chỉ: 123 Đường ABC, Quận 1, TP.HCM");
                System.out.println("📊 GPA hiện tại: 3.5");
            } else {
                System.out.println("✅ Tài khoản Sinh viên đã tồn tại, sẵn sàng đăng nhập!");
            }
        } catch (Exception e) {
            System.err.println("💥 ════════════════════════════════════════");
            System.err.println("💥 LỖI TẠO TÀI KHOẢN SINH VIÊN MẶC ĐỊNH!");
            System.err.println("💥 ════════════════════════════════════════");
            System.err.println("❌ Chi tiết lỗi: " + e.getMessage());
            System.err.println("🔧 Vui lòng kiểm tra cơ sở dữ liệu!");
        }
    }

    public static void main(String[] args) {
        // NOTE: If you use Java 11+ and JavaFX SDK, add VM options:
        // --module-path "path\to\javafx-sdk-XX\lib" --add-modules javafx.controls,javafx.fxml
        // Replace "path\to\javafx-sdk-XX\lib" with your JavaFX SDK lib folder.
        // Example for IntelliJ IDEA: Run/Debug Configurations > VM options

        System.out.println("📚 ════════════════════════════════════════════════════════════");
        System.out.println("📚           KHỞI ĐỘNG STUDENT DASHBOARD");
        System.out.println("📚 ════════════════════════════════════════════════════════════");
        System.out.println("🎓 Chế độ: Student Dashboard (Truy cập trực tiếp)");
        System.out.println("👤 Đăng nhập tự động: Sinh viên hệ thống");
        System.out.println("🎯 Quyền truy cập: Các chức năng học tập");
        System.out.println("📖 Có thể thực hiện:");
        System.out.println("   📚 • Xem danh sách Khóa học đã đăng ký");
        System.out.println("   📊 • Xem Điểm số và kết quả học tập");
        System.out.println("   📅 • Xem Lịch học và Thời khóa biểu");
        System.out.println("   💬 • Chat với Giáo viên");
        System.out.println("   📝 • Đăng ký Khóa học mới");
        System.out.println("   👤 • Quản lý Thông tin cá nhân");
        System.out.println("   📈 • Theo dõi Tiến độ học tập");
        System.out.println("📚 ════════════════════════════════════════════════════════════");
        System.out.println("⏳ Đang khởi động ứng dụng học tập...");

        launch(args);
    }
}