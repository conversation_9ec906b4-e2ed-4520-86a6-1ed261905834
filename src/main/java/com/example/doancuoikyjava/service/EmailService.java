package com.example.doancuoikyjava.service;

import jakarta.mail.*;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;

import java.util.Properties;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.lang.reflect.Method;
import java.lang.reflect.Constructor;

import static java.io.File.separator;
   /**
 * Service để gửi email thông qua Gmail SMTP
 * Phiên bản tạm thời không sử dụng JavaMail để tránh dependency issues
 *
 * CHẾ ĐỘ HOẠT ĐỘNG:
 * - MOCK_MODE = true: Chỉ hiển thị thông tin, không gửi email thật
 * - MOCK_MODE = false: Gửi email thật (cần cài đặt JavaMail)
 */
public class EmailService {

    // Cấu hình chế độ hoạt động - tự động detect Jakarta Mail
    private static final boolean JAKARTA_MAIL_AVAILABLE = checkJakartaMailAvailable();
    private static final boolean MOCK_MODE = !JAKARTA_MAIL_AVAILABLE; // Tự động chuyển sang MOCK nếu không có Jakarta Mail

    private static final String SMTP_HOST = "smtp.gmail.com";
    private static final String SMTP_PORT = "587";
    private static final String EMAIL_USERNAME = "<EMAIL>";
    private static final String EMAIL_PASSWORD = "tzrg pvxf bzig anjj"; // App password

    /**
     * Kiểm tra xem Jakarta Mail có sẵn không
     */
    private static boolean checkJakartaMailAvailable() {
        try {
            // Thử load các class cần thiết của Jakarta Mail
            Class.forName("jakarta.mail.Session");
            Class.forName("jakarta.mail.Transport");
            Class.forName("jakarta.mail.internet.MimeMessage");

            System.out.println("✅ Jakarta Mail dependencies detected - Real email mode enabled");
            return true;
        } catch (ClassNotFoundException e) {
            System.out.println("⚠️ Jakarta Mail not found - Using MOCK mode");
            System.out.println("   To enable real email: Add Jakarta Mail dependencies to your project");
            return false;
        }
    }

    /**
     * Gửi email với mật khẩu mới
     */
    public boolean sendNewPassword(String recipientEmail, String recipientName, String newPassword) {
        if (MOCK_MODE) {
            return sendNewPasswordMock(recipientEmail, recipientName, newPassword);
        } else {
            return sendNewPasswordReal(recipientEmail, recipientName, newPassword);
        }
    }

    /**
     * Gửi email ở chế độ MOCK (chỉ hiển thị thông tin)
     */
    private boolean sendNewPasswordMock(String recipientEmail, String recipientName, String newPassword) {
        try {
            String separator = "==================================================";
            System.out.println("\n" + separator);
            System.out.println("📧 EMAIL SERVICE - CHẾĐỘ MOCK");
            System.out.println(separator);
            System.out.println("📧 Gửi đến: " + recipientEmail);
            System.out.println("👤 Tên: " + recipientName);
            System.out.println("🔑 Mật khẩu mới: " + newPassword);
            System.out.println("🌐 SMTP: " + SMTP_HOST + ":" + SMTP_PORT);
            System.out.println("👤 From: " + EMAIL_USERNAME);

            // Tạo nội dung email
            String emailContent = createEmailContent(recipientName, newPassword);
            System.out.println("📄 Nội dung email HTML: " + emailContent.length() + " ký tự");

            // Giả lập thời gian gửi
            System.out.println("⏳ Đang gửi email...");
            Thread.sleep(1500);

            System.out.println("✅ EMAIL ĐÃ ĐƯỢC MÔ PHỎNG THÀNH CÔNG!");
            System.out.println("📝 LỜI NHẮC QUAN TRỌNG:");
            System.out.println("   🚫 Đây là chế độ MOCK - KHÔNG có email thật được gửi");
            System.out.println("   📧 Người nhận SẼ KHÔNG nhận được email này");
            System.out.println("   ⚠️  Đây chỉ là mô phỏng để test giao diện");
            System.out.println();
            System.out.println("🔧 ĐỂ GỬI EMAIL THẬT:");
            System.out.println("   1. Cần cấu hình Jakarta Mail dependencies đúng cách");
            System.out.println("   2. Sử dụng script build-email.bat");
            System.out.println("   3. Hoặc cấu hình Maven/IDE để download dependencies");
            System.out.println(separator + "\n");

            return true;

        } catch (Exception e) {
            System.err.println("❌ Lỗi trong chế độ MOCK: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Gửi email thật sử dụng Jakarta Mail
     */
    private boolean sendNewPasswordReal(String recipientEmail, String recipientName, String newPassword) {

        try {
            System.out.println("📧 Đang gửi email thật đến: " + recipientEmail);

            // Cấu hình properties cho Gmail SMTP
            Properties props = new Properties();
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.host", SMTP_HOST);
            props.put("mail.smtp.port", SMTP_PORT);
            props.put("mail.smtp.ssl.protocols", "TLSv1.2");

            // Tạo session với authentication
            Session session = Session.getInstance(props, new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(EMAIL_USERNAME, EMAIL_PASSWORD);
                }
            });

            // Tạo message
            Message message = new MimeMessage(session);
            message.setFrom(new InternetAddress(EMAIL_USERNAME, "Hệ thống Quản lý Sinh viên"));
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(recipientEmail));
            message.setSubject("🔑 Mật khẩu mới cho tài khoản của bạn");

            // Tạo nội dung HTML
            String emailContent = createEmailContent(recipientName, newPassword);
            message.setContent(emailContent, "text/html; charset=utf-8");

            // Gửi email
            Transport.send(message);

            System.out.println("✅ Email đã được gửi thành công đến: " + recipientEmail);
            return true;

        } catch (MessagingException e) {
            System.err.println("❌ Lỗi gửi email: " + e.getMessage());
            e.printStackTrace();

            // Phân tích lỗi cụ thể
            if (e.getMessage().contains("Authentication failed")) {
                System.err.println("🔐 Lỗi xác thực - kiểm tra username/password");
            } else if (e.getMessage().contains("Connection")) {
                System.err.println("🌐 Lỗi kết nối - kiểm tra internet và SMTP settings");
            }

            return false;
        } catch (Exception e) {
            System.err.println("❌ Lỗi không xác định: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Tạo nội dung email HTML
     */
    private String createEmailContent(String recipientName, String newPassword) {
        String template = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: linear-gradient(135deg, #667eea 0%%, #764ba2 100%%);
                             color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                    .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
                    .password-box { background: #fff; border: 2px solid #667eea; border-radius: 8px;
                                   padding: 20px; margin: 20px 0; text-align: center; }
                    .password { font-size: 24px; font-weight: bold; color: #667eea;
                               font-family: monospace; letter-spacing: 2px; }
                    .warning { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;
                              padding: 15px; margin: 20px 0; color: #856404; }
                    .footer { text-align: center; margin-top: 30px; color: #666; font-size: 12px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🎓 Hệ thống Quản lý Sinh viên</h1>
                        <p>Mật khẩu mới cho tài khoản của bạn</p>
                    </div>
                    <div class="content">
                        <h2>Xin chào %s!</h2>
                        <p>Chúng tôi đã tạo mật khẩu mới cho tài khoản của bạn theo yêu cầu.</p>

                        <div class="password-box">
                            <p><strong>Mật khẩu mới của bạn là:</strong></p>
                            <div class="password">%s</div>
                        </div>

                        <div class="warning">
                            <strong>⚠️ Lưu ý quan trọng:</strong>
                            <ul>
                                <li>Vui lòng đăng nhập và đổi mật khẩu ngay sau khi nhận được email này</li>
                                <li>Không chia sẻ mật khẩu này với bất kỳ ai</li>
                                <li>Nếu bạn không yêu cầu đặt lại mật khẩu, vui lòng liên hệ quản trị viên</li>
                            </ul>
                        </div>

                        <p>Để đăng nhập:</p>
                        <ol>
                            <li>Mở ứng dụng Hệ thống Quản lý Sinh viên</li>
                            <li>Nhập tên đăng nhập và mật khẩu mới ở trên</li>
                            <li>Chọn vai trò phù hợp</li>
                            <li>Vào phần Hồ sơ để đổi mật khẩu mới</li>
                        </ol>

                        <p>Cảm ơn bạn đã sử dụng hệ thống của chúng tôi!</p>
                    </div>
                    <div class="footer">
                        <p>Email này được gửi tự động từ Hệ thống Quản lý Sinh viên</p>
                        <p>Vui lòng không trả lời email này</p>
                    </div>
                </div>
            </body>
            </html>
            """;

        // Sử dụng String.format thay vì .formatted() để tránh lỗi với CSS
        return String.format(template, recipientName, newPassword);
    }
    
    /**
     * Test kết nối email
     */
    public boolean testConnection() {
        if (MOCK_MODE) {
            return testConnectionMock();
        } else {
            return testConnectionReal();
        }
    }

    /**
     * Test kết nối ở chế độ MOCK
     */
    private boolean testConnectionMock() {
        try {
            String separator = "==================================================";
            System.out.println("\n" + separator);
            System.out.println("🔍 EMAIL CONNECTION TEST - CHẾĐỘ MOCK");
            System.out.println(separator);
            System.out.println("🌐 SMTP Host: " + SMTP_HOST);
            System.out.println("🔌 SMTP Port: " + SMTP_PORT);
            System.out.println("👤 Username: " + EMAIL_USERNAME);
            System.out.println("⏳ Đang kiểm tra kết nối internet...");

            // Kiểm tra kết nối internet
            URL url = new URL("https://www.google.com");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(3000);
            connection.setReadTimeout(3000);

            int responseCode = connection.getResponseCode();
            connection.disconnect();

            if (responseCode == 200) {
                System.out.println("✅ Kết nối internet: THÀNH CÔNG");
                System.out.println("📧 SMTP sẽ hoạt động khi JavaMail được cài đặt");
                System.out.println("🔧 Chế độ hiện tại: MOCK (không gửi email thật)");
                System.out.println(separator + "\n");
                return true;
            } else {
                System.out.println("❌ Kết nối internet thất bại. Response code: " + responseCode);
                System.out.println(separator + "\n");
                return false;
            }

        } catch (Exception e) {
            System.err.println("❌ Lỗi kết nối internet: " + e.getMessage());
            System.out.println("🔧 Chế độ MOCK - không ảnh hưởng chức năng chính");
            System.out.println(separator + "\n");
            return false;
        }
    }

    /**
     * Test kết nối SMTP thật sử dụng Jakarta Mail
     */
    private boolean testConnectionReal() {

        try {
            System.out.println("🔍 Đang test kết nối SMTP thật...");

            // Cấu hình properties cho Gmail SMTP
            Properties props = new Properties();
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.host", SMTP_HOST);
            props.put("mail.smtp.port", SMTP_PORT);
            props.put("mail.smtp.ssl.protocols", "TLSv1.2");
            props.put("mail.smtp.connectiontimeout", "10000");
            props.put("mail.smtp.timeout", "10000");

            // Tạo session với authentication
            Session session = Session.getInstance(props, new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(EMAIL_USERNAME, EMAIL_PASSWORD);
                }
            });

            // Test kết nối
            Transport transport = session.getTransport("smtp");
            transport.connect(SMTP_HOST, EMAIL_USERNAME, EMAIL_PASSWORD);
            transport.close();

            System.out.println("✅ Kết nối SMTP thành công!");
            System.out.println("🌐 Host: " + SMTP_HOST + ":" + SMTP_PORT);
            System.out.println("👤 Username: " + EMAIL_USERNAME);
            return true;

        } catch (MessagingException e) {
            System.err.println("❌ Lỗi kết nối SMTP: " + e.getMessage());

            // Phân tích lỗi cụ thể
            if (e.getMessage().contains("Authentication failed")) {
                System.err.println("🔐 Lỗi xác thực Gmail:");
                System.err.println("   - Kiểm tra username: " + EMAIL_USERNAME);
                System.err.println("   - Kiểm tra App Password (không phải mật khẩu Gmail thường)");
                System.err.println("   - Đảm bảo 2-Step Verification đã bật");
            } else if (e.getMessage().contains("Connection")) {
                System.err.println("🌐 Lỗi kết nối:");
                System.err.println("   - Kiểm tra kết nối internet");
                System.err.println("   - Kiểm tra firewall/antivirus");
                System.err.println("   - SMTP: " + SMTP_HOST + ":" + SMTP_PORT);
            }

            return false;
        } catch (Exception e) {
            System.err.println("❌ Lỗi không xác định: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
}
