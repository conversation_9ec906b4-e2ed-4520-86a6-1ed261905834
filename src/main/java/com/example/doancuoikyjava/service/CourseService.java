package com.example.doancuoikyjava.service;

import com.example.doancuoikyjava.model.Course;
import com.example.doancuoikyjava.util.DataManager;
import com.example.doancuoikyjava.util.DatabaseConnection;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public class CourseService {

    private boolean useDatabaseStorage;

    public CourseService() {
        useDatabaseStorage = DatabaseConnection.testConnection();
        if (useDatabaseStorage) {
            System.out.println("📚 CourseService: Sử dụng SQL Server Database");
        } else {
            System.out.println("📁 CourseService: Sử dụng File-based Storage");
        }
    }

    public List<Course> getAllCourses() {
        if (useDatabaseStorage) {
            return getAllCoursesFromDatabase();
        } else {
            return DataManager.loadCourses();
        }
    }

    public Course getCourseById(String courseId) {
        if (useDatabaseStorage) {
            return getCourseByIdFromDatabase(courseId);
        } else {
            return DataManager.loadCourses().stream()
                    .filter(course -> course.getCourseId().equals(courseId))
                    .findFirst()
                    .orElse(null);
        }
    }

    public List<Course> getCoursesByTeacher(String teacherId) {
        if (useDatabaseStorage) {
            return getCoursesByTeacherFromDatabase(teacherId);
        } else {
            return DataManager.loadCourses().stream()
                    .filter(course -> course.getTeacherId().equals(teacherId))
                    .toList();
        }
    }
    
    // Database methods
    private List<Course> getAllCoursesFromDatabase() {
        List<Course> courses = new ArrayList<>();
        String sql = """
            SELECT c.*,
                   (SELECT COUNT(*) FROM Course_Enrollments ce WHERE ce.course_id = c.course_id) as enrolled_count
            FROM Courses c
            ORDER BY c.course_id
            """;

        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            ResultSet rs = stmt.executeQuery();

            while (rs.next()) {
                Course course = createCourseFromResultSet(rs);
                // Load enrolled students
                loadEnrolledStudents(course);
                courses.add(course);
            }

        } catch (SQLException e) {
            System.err.println("❌ Lỗi lấy danh sách courses: " + e.getMessage());
        }

        return courses;
    }

    private Course getCourseByIdFromDatabase(String courseId) {
        String sql = "SELECT * FROM Courses WHERE course_id = ?";

        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, courseId);
            ResultSet rs = stmt.executeQuery();

            if (rs.next()) {
                Course course = createCourseFromResultSet(rs);
                loadEnrolledStudents(course);
                return course;
            }

        } catch (SQLException e) {
            System.err.println("❌ Lỗi lấy course theo ID: " + e.getMessage());
        }

        return null;
    }

    private List<Course> getCoursesByTeacherFromDatabase(String teacherId) {
        List<Course> courses = new ArrayList<>();
        String sql = "SELECT * FROM Courses WHERE teacher_id = ? ORDER BY course_id";

        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, teacherId);
            ResultSet rs = stmt.executeQuery();

            while (rs.next()) {
                Course course = createCourseFromResultSet(rs);
                loadEnrolledStudents(course);
                courses.add(course);
            }

        } catch (SQLException e) {
            System.err.println("❌ Lỗi lấy courses theo teacher: " + e.getMessage());
        }

        return courses;
    }

    private Course createCourseFromResultSet(ResultSet rs) throws SQLException {
        Course course = new Course();
        course.setCourseId(rs.getString("course_id"));
        course.setCourseName(rs.getString("course_name"));
        course.setDescription(rs.getString("description"));
        course.setCredits(rs.getInt("credits"));
        course.setTeacherId(rs.getString("teacher_id"));
        course.setTeacherName(rs.getString("teacher_name"));
        course.setSchedule(rs.getString("schedule"));
        course.setClassroom(rs.getString("classroom"));
        course.setMaxStudents(rs.getInt("max_students"));
        return course;
    }

    private void loadEnrolledStudents(Course course) {
        String sql = "SELECT student_id FROM Course_Enrollments WHERE course_id = ?";

        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, course.getCourseId());
            ResultSet rs = stmt.executeQuery();

            List<String> enrolledStudents = new ArrayList<>();
            while (rs.next()) {
                enrolledStudents.add(rs.getString("student_id"));
            }
            course.setEnrolledStudents(enrolledStudents);

        } catch (SQLException e) {
            System.err.println("❌ Lỗi load enrolled students: " + e.getMessage());
        }
    }

    public boolean addCourse(Course course) {
        if (useDatabaseStorage) {
            return addCourseToDatabase(course);
        } else {
            List<Course> courses = DataManager.loadCourses();

            // Check if course ID already exists
            boolean courseExists = courses.stream()
                    .anyMatch(c -> c.getCourseId().equals(course.getCourseId()));

            if (courseExists) {
                return false;
            }

            courses.add(course);
            DataManager.saveCourses(courses);
            return true;
        }
    }

    private boolean addCourseToDatabase(Course course) {
        String sql = """
            INSERT INTO Courses (course_id, course_name, description, credits, teacher_id, teacher_name,
                               schedule, classroom, max_students)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;

        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, course.getCourseId());
            stmt.setString(2, course.getCourseName());
            stmt.setString(3, course.getDescription());
            stmt.setInt(4, course.getCredits());
            stmt.setString(5, course.getTeacherId());
            stmt.setString(6, course.getTeacherName());
            stmt.setString(7, course.getSchedule());
            stmt.setString(8, course.getClassroom());
            stmt.setInt(9, course.getMaxStudents());

            int result = stmt.executeUpdate();

            // Đồng bộ với Teacher_Courses nếu thêm thành công
            if (result > 0 && course.getTeacherId() != null && course.getCourseName() != null) {
                syncTeacherCourse(course.getTeacherId(), course.getCourseName());
            }

            return result > 0;

        } catch (SQLException e) {
            System.err.println("❌ Lỗi thêm course: " + e.getMessage());
            return false;
        }
    }

    public boolean updateCourse(Course course) {
        if (useDatabaseStorage) {
            return updateCourseInDatabase(course);
        } else {
            List<Course> courses = DataManager.loadCourses();

            for (int i = 0; i < courses.size(); i++) {
                if (courses.get(i).getCourseId().equals(course.getCourseId())) {
                    courses.set(i, course);
                    DataManager.saveCourses(courses);
                    return true;
                }
            }
            return false;
        }
    }

    private boolean updateCourseInDatabase(Course course) {
        String sql = """
            UPDATE Courses SET course_name = ?, description = ?, credits = ?, teacher_id = ?,
                             teacher_name = ?, schedule = ?, classroom = ?, max_students = ?
            WHERE course_id = ?
            """;

        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, course.getCourseName());
            stmt.setString(2, course.getDescription());
            stmt.setInt(3, course.getCredits());
            stmt.setString(4, course.getTeacherId());
            stmt.setString(5, course.getTeacherName());
            stmt.setString(6, course.getSchedule());
            stmt.setString(7, course.getClassroom());
            stmt.setInt(8, course.getMaxStudents());
            stmt.setString(9, course.getCourseId());

            int result = stmt.executeUpdate();
            return result > 0;

        } catch (SQLException e) {
            System.err.println("❌ Lỗi cập nhật course: " + e.getMessage());
            return false;
        }
    }

    public boolean deleteCourse(String courseId) {
        if (useDatabaseStorage) {
            return deleteCourseFromDatabase(courseId);
        } else {
            List<Course> courses = DataManager.loadCourses();
            boolean removed = courses.removeIf(course -> course.getCourseId().equals(courseId));

            if (removed) {
                DataManager.saveCourses(courses);
            }
            return removed;
        }
    }

    private boolean deleteCourseFromDatabase(String courseId) {
        String sql = "DELETE FROM Courses WHERE course_id = ?";

        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, courseId);

            int result = stmt.executeUpdate();
            return result > 0;

        } catch (SQLException e) {
            System.err.println("❌ Lỗi xóa course: " + e.getMessage());
            return false;
        }
    }

    public boolean enrollStudent(String courseId, String studentId) {
        if (useDatabaseStorage) {
            return enrollStudentInDatabase(courseId, studentId);
        } else {
            List<Course> courses = DataManager.loadCourses();

            for (Course course : courses) {
                if (course.getCourseId().equals(courseId)) {
                    if (!course.getEnrolledStudents().contains(studentId)) {
                        course.getEnrolledStudents().add(studentId);
                        DataManager.saveCourses(courses);
                        return true;
                    }
                }
            }
            return false;
        }
    }

    private boolean enrollStudentInDatabase(String courseId, String studentId) {
        String sql = "INSERT INTO Course_Enrollments (course_id, student_id) VALUES (?, ?)";

        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, courseId);
            stmt.setString(2, studentId);

            int result = stmt.executeUpdate();
            return result > 0;

        } catch (SQLException e) {
            System.err.println("❌ Lỗi đăng ký sinh viên: " + e.getMessage());
            return false;
        }
    }

    public boolean unenrollStudent(String courseId, String studentId) {
        if (useDatabaseStorage) {
            return unenrollStudentFromDatabase(courseId, studentId);
        } else {
            List<Course> courses = DataManager.loadCourses();

            for (Course course : courses) {
                if (course.getCourseId().equals(courseId)) {
                    boolean removed = course.getEnrolledStudents().remove(studentId);
                    if (removed) {
                        DataManager.saveCourses(courses);
                        return true;
                    }
                }
            }
            return false;
        }
    }

    private boolean unenrollStudentFromDatabase(String courseId, String studentId) {
        String sql = "DELETE FROM Course_Enrollments WHERE course_id = ? AND student_id = ?";

        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, courseId);
            stmt.setString(2, studentId);

            int result = stmt.executeUpdate();
            return result > 0;

        } catch (SQLException e) {
            System.err.println("❌ Lỗi hủy đăng ký sinh viên: " + e.getMessage());
            return false;
        }
    }

    public long getTotalCourses() {
        if (useDatabaseStorage) {
            try {
                Connection conn = DatabaseConnection.getConnection();
                PreparedStatement stmt = conn.prepareStatement("SELECT COUNT(*) FROM Courses");
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    return rs.getLong(1);
                }
            } catch (SQLException e) {
                System.err.println("❌ Lỗi đếm courses: " + e.getMessage());
            }
            return 0;
        } else {
            return DataManager.loadCourses().size();
        }
    }

    public String generateNextCourseId() {
        if (useDatabaseStorage) {
            try {
                Connection conn = DatabaseConnection.getConnection();
                PreparedStatement stmt = conn.prepareStatement(
                    "SELECT MAX(CAST(SUBSTRING(course_id, 3, LEN(course_id)) AS INT)) FROM Courses WHERE course_id LIKE 'CS%'"
                );
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    int maxNumber = rs.getInt(1);
                    return "CS" + (maxNumber + 1);
                }
            } catch (SQLException e) {
                System.err.println("❌ Lỗi tạo course ID: " + e.getMessage());
            }
            return "CS101";
        } else {
            List<Course> courses = DataManager.loadCourses();

            int maxNumber = courses.stream()
                    .filter(course -> course.getCourseId().matches("CS\\d+"))
                    .mapToInt(course -> {
                        try {
                            return Integer.parseInt(course.getCourseId().substring(2));
                        } catch (NumberFormatException e) {
                            return 0;
                        }
                    })
                    .max()
                    .orElse(100);

            return "CS" + (maxNumber + 1);
        }
    }

    /**
     * Đồng bộ môn học với bảng Teacher_Courses
     */
    private void syncTeacherCourse(String teacherId, String courseName) {
        if (teacherId == null || courseName == null) return;

        String sql = """
            IF NOT EXISTS (SELECT 1 FROM Teacher_Courses WHERE teacher_id = ? AND course_name = ?)
            INSERT INTO Teacher_Courses (teacher_id, course_name) VALUES (?, ?)
            """;

        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, teacherId);
            stmt.setString(2, courseName);
            stmt.setString(3, teacherId);
            stmt.setString(4, courseName);

            stmt.executeUpdate();
            System.out.println("✅ Đồng bộ môn dạy: " + courseName + " cho giáo viên " + teacherId);

        } catch (SQLException e) {
            System.err.println("❌ Lỗi đồng bộ teacher course: " + e.getMessage());
        }
    }
}
