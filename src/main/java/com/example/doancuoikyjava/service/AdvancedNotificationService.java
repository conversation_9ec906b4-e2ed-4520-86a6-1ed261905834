package com.example.doancuoikyjava.service;

import com.example.doancuoikyjava.model.Notification;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.util.CacheManager;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Advanced Notification Service with real-time updates, caching, and background processing
 * Simplified version without database dependency
 */
public class AdvancedNotificationService {

    private static AdvancedNotificationService instance;
    private final CacheManager cacheManager;
    private final ExecutorService backgroundExecutor;
    private final List<NotificationListener> listeners;
    private final ConcurrentHashMap<String, List<Notification>> userNotifications;
    
    // Notification types
    public enum NotificationType {
        INFO("📢", "#2196F3"),
        SUCCESS("✅", "#4CAF50"),
        WARNING("⚠️", "#FF9800"),
        ERROR("❌", "#F44336"),
        URGENT("🚨", "#E91E63"),
        SYSTEM("⚙️", "#9C27B0"),
        GRADE("📝", "#3F51B5"),
        SCHEDULE("📅", "#00BCD4"),
        CHAT("💬", "#4CAF50");
        
        private final String icon;
        private final String color;
        
        NotificationType(String icon, String color) {
            this.icon = icon;
            this.color = color;
        }
        
        public String getIcon() { return icon; }
        public String getColor() { return color; }
    }
    
    // Notification priority levels
    public enum Priority {
        LOW(1), NORMAL(2), HIGH(3), URGENT(4);
        
        private final int level;
        
        Priority(int level) {
            this.level = level;
        }
        
        public int getLevel() { return level; }
    }
    
    /**
     * Notification listener interface
     */
    public interface NotificationListener {
        void onNotificationReceived(Notification notification);
        void onNotificationRead(String notificationId);
        void onNotificationDeleted(String notificationId);
    }
    
    private AdvancedNotificationService() {
        this.cacheManager = CacheManager.getInstance();
        this.backgroundExecutor = Executors.newFixedThreadPool(3, r -> {
            Thread t = new Thread(r, "NotificationProcessor");
            t.setDaemon(true);
            return t;
        });
        this.listeners = new ArrayList<>();
        this.userNotifications = new ConcurrentHashMap<>();

        System.out.println("📢 Advanced Notification Service initialized (simplified mode)");
    }
    
    public static synchronized AdvancedNotificationService getInstance() {
        if (instance == null) {
            instance = new AdvancedNotificationService();
        }
        return instance;
    }
    
    /**
     * Send notification to specific user
     */
    public CompletableFuture<Boolean> sendNotification(String recipientId, String title, String message, 
                                                      NotificationType type, Priority priority) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                Notification notification = new Notification();
                notification.setId(generateNotificationId());
                notification.setRecipientId(recipientId);
                notification.setTitle(title);
                notification.setMessage(message);
                notification.setTypeString(type.name());
                notification.setPriorityString(priority.name());
                notification.setCreatedAt(LocalDateTime.now());
                notification.setRead(false);
                
                // Save to in-memory storage
                boolean saved = saveNotificationToMemory(notification);

                if (saved) {
                    // Clear cache for this user
                    cacheManager.remove(CacheManager.Keys.notificationKey(recipientId));

                    // Notify listeners
                    notifyListeners(notification);

                    System.out.println("📤 Notification sent to " + recipientId + ": " + title);
                    return true;
                }

                return false;
                
            } catch (Exception e) {
                System.err.println("❌ Failed to send notification: " + e.getMessage());
                return false;
            }
        }, backgroundExecutor);
    }
    
    /**
     * Send notification to multiple users
     */
    public CompletableFuture<Integer> sendBulkNotification(List<String> recipientIds, String title, 
                                                          String message, NotificationType type, Priority priority) {
        return CompletableFuture.supplyAsync(() -> {
            int successCount = 0;
            
            for (String recipientId : recipientIds) {
                try {
                    boolean sent = sendNotification(recipientId, title, message, type, priority).get();
                    if (sent) {
                        successCount++;
                    }
                } catch (Exception e) {
                    System.err.println("❌ Failed to send notification to " + recipientId + ": " + e.getMessage());
                }
            }
            
            System.out.println("📤 Bulk notification sent to " + successCount + "/" + recipientIds.size() + " users");
            return successCount;
            
        }, backgroundExecutor);
    }
    
    /**
     * Get notifications for user with caching
     */
    public List<Notification> getNotificationsForUser(String userId) {
        return getNotificationsForUser(userId, false);
    }
    
    /**
     * Get notifications for user with option to include read notifications
     */
    public List<Notification> getNotificationsForUser(String userId, boolean includeRead) {
        String cacheKey = CacheManager.Keys.notificationKey(userId) + (includeRead ? ":all" : ":unread");
        
        return cacheManager.getOrCompute(cacheKey, () -> {
            return loadNotificationsFromMemory(userId, includeRead);
        }, CacheManager.SHORT_TTL);
    }
    
    /**
     * Get unread notification count for user
     */
    public int getUnreadCount(String userId) {
        String cacheKey = CacheManager.Keys.notificationKey(userId) + ":count";
        
        return cacheManager.getOrCompute(cacheKey, () -> {
            return loadUnreadCountFromMemory(userId);
        }, CacheManager.SHORT_TTL);
    }
    
    /**
     * Mark notification as read
     */
    public CompletableFuture<Boolean> markAsRead(String notificationId, String userId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                List<Notification> userNotifs = userNotifications.get(userId);
                if (userNotifs != null) {
                    for (Notification notification : userNotifs) {
                        if (notificationId.equals(notification.getId())) {
                            notification.setRead(true);
                            notification.setReadAt(LocalDateTime.now());

                            // Clear cache
                            cacheManager.clearByPrefix(CacheManager.Keys.notificationKey(userId));

                            // Notify listeners
                            for (NotificationListener listener : listeners) {
                                listener.onNotificationRead(notificationId);
                            }

                            return true;
                        }
                    }
                }

                return false;

            } catch (Exception e) {
                System.err.println("❌ Failed to mark notification as read: " + e.getMessage());
                return false;
            }
        }, backgroundExecutor);
    }
    
    /**
     * Mark all notifications as read for user
     */
    public CompletableFuture<Boolean> markAllAsRead(String userId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                List<Notification> userNotifs = userNotifications.get(userId);
                if (userNotifs != null) {
                    int updated = 0;
                    LocalDateTime now = LocalDateTime.now();

                    for (Notification notification : userNotifs) {
                        if (!notification.isRead()) {
                            notification.setRead(true);
                            notification.setReadAt(now);
                            updated++;
                        }
                    }

                    if (updated > 0) {
                        // Clear cache
                        cacheManager.clearByPrefix(CacheManager.Keys.notificationKey(userId));

                        System.out.println("📖 Marked " + updated + " notifications as read for user " + userId);
                        return true;
                    }
                }

                return false;

            } catch (Exception e) {
                System.err.println("❌ Failed to mark all notifications as read: " + e.getMessage());
                return false;
            }
        }, backgroundExecutor);
    }
    
    /**
     * Delete notification
     */
    public CompletableFuture<Boolean> deleteNotification(String notificationId, String userId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                List<Notification> userNotifs = userNotifications.get(userId);
                if (userNotifs != null) {
                    boolean removed = userNotifs.removeIf(n -> notificationId.equals(n.getId()));

                    if (removed) {
                        // Clear cache
                        cacheManager.clearByPrefix(CacheManager.Keys.notificationKey(userId));

                        // Notify listeners
                        for (NotificationListener listener : listeners) {
                            listener.onNotificationDeleted(notificationId);
                        }

                        return true;
                    }
                }

                return false;

            } catch (Exception e) {
                System.err.println("❌ Failed to delete notification: " + e.getMessage());
                return false;
            }
        }, backgroundExecutor);
    }
    
    /**
     * Clean up old notifications (older than specified days)
     */
    public CompletableFuture<Integer> cleanupOldNotifications(int daysOld) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                int deleted = 0;
                LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysOld);

                for (List<Notification> userNotifs : userNotifications.values()) {
                    deleted += userNotifs.removeIf(n -> n.getCreatedAt().isBefore(cutoffDate)) ? 1 : 0;
                }

                if (deleted > 0) {
                    // Clear all notification caches
                    cacheManager.clearByPrefix(CacheManager.Keys.NOTIFICATION_PREFIX);
                    System.out.println("🧹 Cleaned up " + deleted + " old notifications");
                }

                return deleted;

            } catch (Exception e) {
                System.err.println("❌ Failed to cleanup old notifications: " + e.getMessage());
                return 0;
            }
        }, backgroundExecutor);
    }
    
    /**
     * Add notification listener
     */
    public void addNotificationListener(NotificationListener listener) {
        listeners.add(listener);
    }
    
    /**
     * Remove notification listener
     */
    public void removeNotificationListener(NotificationListener listener) {
        listeners.remove(listener);
    }
    
    /**
     * Create system notification for all users
     */
    public CompletableFuture<Integer> createSystemNotification(String title, String message, Priority priority) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // For demo purposes, create sample user IDs
                List<String> userIds = new ArrayList<>();
                userIds.add("user1");
                userIds.add("user2");
                userIds.add("user3");

                // Send notification to all users
                return sendBulkNotification(userIds, title, message, NotificationType.SYSTEM, priority).get();

            } catch (Exception e) {
                System.err.println("❌ Failed to create system notification: " + e.getMessage());
                return 0;
            }
        }, backgroundExecutor);
    }
    
    /**
     * Save notification to in-memory storage
     */
    private boolean saveNotificationToMemory(Notification notification) {
        try {
            String recipientId = notification.getRecipientId();
            userNotifications.computeIfAbsent(recipientId, k -> new ArrayList<>()).add(notification);
            System.out.println("💾 Notification saved to memory for user: " + recipientId);
            return true;
        } catch (Exception e) {
            System.err.println("❌ Failed to save notification to memory: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Load notifications from memory
     */
    private List<Notification> loadNotificationsFromMemory(String userId, boolean includeRead) {
        List<Notification> notifications = new ArrayList<>();

        try {
            List<Notification> userNotifs = userNotifications.get(userId);
            if (userNotifs != null) {
                for (Notification notification : userNotifs) {
                    if (includeRead || !notification.isRead()) {
                        notifications.add(notification);
                    }
                }
                // Sort by creation time (newest first)
                notifications.sort((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()));
            }
        } catch (Exception e) {
            System.err.println("❌ Failed to load notifications from memory: " + e.getMessage());
        }

        return notifications;
    }
    
    /**
     * Load unread count from memory
     */
    private int loadUnreadCountFromMemory(String userId) {
        try {
            List<Notification> userNotifs = userNotifications.get(userId);
            if (userNotifs != null) {
                return (int) userNotifs.stream().filter(n -> !n.isRead()).count();
            }
        } catch (Exception e) {
            System.err.println("❌ Failed to load unread count from memory: " + e.getMessage());
        }

        return 0;
    }
    
    /**
     * Generate unique notification ID
     */
    private String generateNotificationId() {
        return "NOTIF_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }
    
    /**
     * Notify all listeners
     */
    private void notifyListeners(Notification notification) {
        for (NotificationListener listener : listeners) {
            try {
                listener.onNotificationReceived(notification);
            } catch (Exception e) {
                System.err.println("❌ Error notifying listener: " + e.getMessage());
            }
        }
    }
    
    /**
     * Shutdown service
     */
    public void shutdown() {
        backgroundExecutor.shutdown();
        try {
            if (!backgroundExecutor.awaitTermination(5, java.util.concurrent.TimeUnit.SECONDS)) {
                backgroundExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            backgroundExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        System.out.println("🔒 Advanced Notification Service shutdown completed");
    }
}
