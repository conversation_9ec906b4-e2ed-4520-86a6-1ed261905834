package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.service.*;
import javafx.collections.FXCollections;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

import java.net.URL;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;

public class EditTeacherScheduleDialogController implements Initializable {
    
    @FXML private Label titleLabel;
    @FXML private Label courseNameLabel;
    @FXML private Label creditsLabel;
    @FXML private Label studentsCountLabel;
    
    @FXML private ComboBox<String> dayComboBox;
    @FXML private ComboBox<String> startTimeComboBox;
    @FXML private ComboBox<String> endTimeComboBox;
    @FXML private ComboBox<String> classroomComboBox;
    @FXML private ComboBox<String> weekTypeComboBox;
    @FXML private ComboBox<String> statusComboBox;
    
    @FXML private VBox conflictCheckBox;
    @FXML private Label conflictResultLabel;
    @FXML private Label previewLabel;
    @FXML private TextArea notesTextArea;
    
    @FXML private Button checkConflictBtn;
    @FXML private Button saveBtn;
    
    private ScheduleService scheduleService;
    private CourseService courseService;
    private ScheduleInfo editingScheduleInfo;
    private CourseSchedule editingCourseSchedule;
    private boolean isSaved = false;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        scheduleService = new ScheduleService();
        courseService = new CourseService();
        
        setupComboBoxes();
        setupEventHandlers();
    }
    
    private void setupComboBoxes() {
        // Day of week
        dayComboBox.setItems(FXCollections.observableArrayList(
            "Thứ 2", "Thứ 3", "Thứ 4", "Thứ 5", "Thứ 6", "Thứ 7", "Chủ nhật"
        ));
        
        // Time slots
        String[] timeSlots = {
            "07:30", "08:30", "09:30", "09:45", "10:45", "11:45",
            "13:30", "14:30", "15:30", "16:30", "17:30", "18:00", "19:00", "20:00"
        };
        startTimeComboBox.setItems(FXCollections.observableArrayList(timeSlots));
        endTimeComboBox.setItems(FXCollections.observableArrayList(timeSlots));
        
        // Classrooms (1-50)
        String[] classrooms = new String[50];
        for (int i = 1; i <= 50; i++) {
            classrooms[i-1] = "Phòng " + i;
        }
        classroomComboBox.setItems(FXCollections.observableArrayList(classrooms));
        
        // Week types
        weekTypeComboBox.setItems(FXCollections.observableArrayList(
            "Tất cả các tuần", "Tuần lẻ", "Tuần chẵn"
        ));
        weekTypeComboBox.setValue("Tất cả các tuần");
        
        // Status
        statusComboBox.setItems(FXCollections.observableArrayList(
            "Đang diễn ra", "Tạm hoãn", "Đã hủy", "Chưa xếp lịch"
        ));
        statusComboBox.setValue("Đang diễn ra");
    }
    
    private void setupEventHandlers() {
        // Update preview when any field changes
        dayComboBox.setOnAction(e -> updatePreview());
        startTimeComboBox.setOnAction(e -> updatePreview());
        endTimeComboBox.setOnAction(e -> updatePreview());
        classroomComboBox.setOnAction(e -> updatePreview());
        weekTypeComboBox.setOnAction(e -> updatePreview());
        statusComboBox.setOnAction(e -> updatePreview());
        
        // Auto-set end time when start time is selected
        startTimeComboBox.setOnAction(e -> {
            String startTime = startTimeComboBox.getValue();
            if (startTime != null && !startTime.isEmpty()) {
                try {
                    LocalTime start = LocalTime.parse(startTime);
                    LocalTime end = start.plusHours(2); // Default 2-hour duration
                    endTimeComboBox.setValue(end.format(DateTimeFormatter.ofPattern("HH:mm")));
                } catch (Exception ex) {
                    System.err.println("Error setting end time: " + ex.getMessage());
                }
            }
            updatePreview();
        });
    }
    
    public void setScheduleInfo(ScheduleInfo scheduleInfo) {
        this.editingScheduleInfo = scheduleInfo;
        
        if (scheduleInfo != null) {
            // Update course information
            courseNameLabel.setText(scheduleInfo.getCourseName());
            creditsLabel.setText(String.valueOf(scheduleInfo.getCredits()));
            studentsCountLabel.setText(String.valueOf(scheduleInfo.getStudentsCount()));
            
            // Set current values
            dayComboBox.setValue(scheduleInfo.getDay());
            statusComboBox.setValue(scheduleInfo.getStatus());
            
            // Parse time range
            String timeRange = scheduleInfo.getTime();
            if (timeRange != null && timeRange.contains(" - ")) {
                String[] times = timeRange.split(" - ");
                if (times.length == 2) {
                    startTimeComboBox.setValue(times[0].trim());
                    endTimeComboBox.setValue(times[1].trim());
                }
            }
            
            // Set classroom
            if (scheduleInfo.getClassroom() != null && !scheduleInfo.getClassroom().equals("TBA")) {
                classroomComboBox.setValue(scheduleInfo.getClassroom());
            }
            
            updatePreview();
        }
    }
    
    public void setCourseSchedule(CourseSchedule courseSchedule) {
        this.editingCourseSchedule = courseSchedule;
        
        if (courseSchedule != null) {
            // Update course information
            courseNameLabel.setText(courseSchedule.getCourseName());
            
            // Get course details
            Course course = courseService.getCourseById(courseSchedule.getCourseId());
            if (course != null) {
                creditsLabel.setText(String.valueOf(course.getCredits()));
                studentsCountLabel.setText(String.valueOf(course.getEnrolledStudents().size()));
            }
            
            // Set current values
            dayComboBox.setValue(courseSchedule.getDayOfWeekText());
            startTimeComboBox.setValue(courseSchedule.getStartTime().format(DateTimeFormatter.ofPattern("HH:mm")));
            endTimeComboBox.setValue(courseSchedule.getEndTime().format(DateTimeFormatter.ofPattern("HH:mm")));
            classroomComboBox.setValue(courseSchedule.getClassroom());
            
            // Set week type
            String weekType = courseSchedule.getWeekType();
            switch (weekType) {
                case "ALL" -> weekTypeComboBox.setValue("Tất cả các tuần");
                case "ODD" -> weekTypeComboBox.setValue("Tuần lẻ");
                case "EVEN" -> weekTypeComboBox.setValue("Tuần chẵn");
                default -> weekTypeComboBox.setValue("Tất cả các tuần");
            }
            
            statusComboBox.setValue(courseSchedule.isActive() ? "Đang diễn ra" : "Tạm hoãn");
            
            updatePreview();
        }
    }
    
    private void updatePreview() {
        StringBuilder preview = new StringBuilder();
        
        String day = dayComboBox.getValue();
        String startTime = startTimeComboBox.getValue();
        String endTime = endTimeComboBox.getValue();
        String classroom = classroomComboBox.getValue();
        String weekType = weekTypeComboBox.getValue();
        String status = statusComboBox.getValue();
        
        if (day != null && startTime != null && endTime != null) {
            preview.append("📅 ").append(day).append(" ");
            preview.append("🕐 ").append(startTime).append(" - ").append(endTime).append(" ");
            
            if (classroom != null) {
                preview.append("🏫 ").append(classroom).append(" ");
            }
            
            if (weekType != null && !weekType.equals("Tất cả các tuần")) {
                preview.append("(").append(weekType).append(") ");
            }
            
            if (status != null) {
                preview.append("📊 ").append(status);
            }
        } else {
            preview.append("Vui lòng chọn đầy đủ thông tin để xem trước");
        }
        
        previewLabel.setText(preview.toString());
    }
    
    @FXML
    private void checkConflicts() {
        if (!isFormValid()) {
            showAlert("Lỗi", "Vui lòng điền đầy đủ thông tin bắt buộc!");
            return;
        }
        
        try {
            CourseSchedule tempSchedule = createScheduleFromForm();
            boolean hasConflict = scheduleService.hasScheduleConflict(tempSchedule);
            
            conflictCheckBox.setVisible(true);
            
            if (hasConflict) {
                conflictResultLabel.setText("❌ PHÁT HIỆN XUNG ĐỘT!\n" +
                    "Đã có lịch học khác trong cùng thời gian và phòng học này.\n" +
                    "Vui lòng chọn thời gian hoặc phòng học khác.");
                conflictCheckBox.setStyle("-fx-background-color: #FFEBEE; -fx-border-color: #F44336;");
                saveBtn.setDisable(true);
            } else {
                conflictResultLabel.setText("✅ KHÔNG CÓ XUNG ĐỘT!\n" +
                    "Lịch học này có thể được cập nhật an toàn.");
                conflictCheckBox.setStyle("-fx-background-color: #E8F5E8; -fx-border-color: #4CAF50;");
                saveBtn.setDisable(false);
            }
            
        } catch (Exception e) {
            showAlert("Lỗi", "Lỗi khi kiểm tra xung đột: " + e.getMessage());
        }
    }
    
    @FXML
    private void save() {
        if (!isFormValid()) {
            showAlert("Lỗi", "Vui lòng điền đầy đủ thông tin bắt buộc!");
            return;
        }
        
        try {
            if (editingCourseSchedule != null) {
                // Store original schedule info for comparison
                CourseSchedule originalSchedule = new CourseSchedule();
                originalSchedule.setCourseId(editingCourseSchedule.getCourseId());
                originalSchedule.setDayOfWeek(editingCourseSchedule.getDayOfWeek());
                originalSchedule.setStartTime(editingCourseSchedule.getStartTime());
                originalSchedule.setEndTime(editingCourseSchedule.getEndTime());
                originalSchedule.setClassroom(editingCourseSchedule.getClassroom());

                // Update existing CourseSchedule
                updateCourseScheduleFromForm();
                boolean success = scheduleService.updateSchedule(editingCourseSchedule);

                if (success) {
                    // Synchronize with student schedules
                    synchronizeStudentSchedules(originalSchedule, editingCourseSchedule);

                    // Update course schedule string for backward compatibility
                    updateCourseScheduleString(editingCourseSchedule);

                    isSaved = true;
                    showAlert("Thành công",
                        "Đã cập nhật lịch dạy thành công!\n" +
                        "Lịch học của sinh viên đã được đồng bộ tự động.");
                    closeDialog();
                } else {
                    showAlert("Lỗi", "Không thể cập nhật lịch dạy. Vui lòng thử lại!");
                }
            } else {
                // This is editing from ScheduleInfo (fallback data)
                showAlert("Thông báo", "Chức năng này đang được phát triển cho dữ liệu fallback.\n" +
                    "Vui lòng sử dụng tính năng quản lý lịch học từ admin để tạo lịch chi tiết.");
            }

        } catch (Exception e) {
            System.err.println("❌ Error saving schedule: " + e.getMessage());
            showAlert("Lỗi", "Lỗi khi lưu lịch dạy: " + e.getMessage());
        }
    }
    
    @FXML
    private void cancel() {
        closeDialog();
    }
    
    private boolean isFormValid() {
        return dayComboBox.getValue() != null &&
               startTimeComboBox.getValue() != null &&
               endTimeComboBox.getValue() != null &&
               classroomComboBox.getValue() != null;
    }
    
    private CourseSchedule createScheduleFromForm() {
        CourseSchedule schedule = new CourseSchedule();
        
        if (editingCourseSchedule != null) {
            schedule.setScheduleId(editingCourseSchedule.getScheduleId());
            schedule.setCourseId(editingCourseSchedule.getCourseId());
            schedule.setTeacherId(editingCourseSchedule.getTeacherId());
        }
        
        // Set day of week
        String day = dayComboBox.getValue();
        int dayOfWeek = switch (day) {
            case "Thứ 2" -> 2;
            case "Thứ 3" -> 3;
            case "Thứ 4" -> 4;
            case "Thứ 5" -> 5;
            case "Thứ 6" -> 6;
            case "Thứ 7" -> 7;
            case "Chủ nhật" -> 8;
            default -> 2;
        };
        schedule.setDayOfWeek(dayOfWeek);
        
        // Set times
        schedule.setStartTime(LocalTime.parse(startTimeComboBox.getValue()));
        schedule.setEndTime(LocalTime.parse(endTimeComboBox.getValue()));
        
        // Set classroom
        schedule.setClassroom(classroomComboBox.getValue());
        
        // Set week type
        String weekType = weekTypeComboBox.getValue();
        String weekTypeCode = switch (weekType) {
            case "Tuần lẻ" -> "ODD";
            case "Tuần chẵn" -> "EVEN";
            default -> "ALL";
        };
        schedule.setWeekType(weekTypeCode);
        
        // Set status
        schedule.setActive(statusComboBox.getValue().equals("Đang diễn ra"));
        
        return schedule;
    }
    
    private void updateCourseScheduleFromForm() {
        if (editingCourseSchedule == null) return;
        
        // Update day of week
        String day = dayComboBox.getValue();
        int dayOfWeek = switch (day) {
            case "Thứ 2" -> 2;
            case "Thứ 3" -> 3;
            case "Thứ 4" -> 4;
            case "Thứ 5" -> 5;
            case "Thứ 6" -> 6;
            case "Thứ 7" -> 7;
            case "Chủ nhật" -> 8;
            default -> 2;
        };
        editingCourseSchedule.setDayOfWeek(dayOfWeek);
        
        // Update times
        editingCourseSchedule.setStartTime(LocalTime.parse(startTimeComboBox.getValue()));
        editingCourseSchedule.setEndTime(LocalTime.parse(endTimeComboBox.getValue()));
        
        // Update classroom
        editingCourseSchedule.setClassroom(classroomComboBox.getValue());
        
        // Update week type
        String weekType = weekTypeComboBox.getValue();
        String weekTypeCode = switch (weekType) {
            case "Tuần lẻ" -> "ODD";
            case "Tuần chẵn" -> "EVEN";
            default -> "ALL";
        };
        editingCourseSchedule.setWeekType(weekTypeCode);
        
        // Update status
        editingCourseSchedule.setActive(statusComboBox.getValue().equals("Đang diễn ra"));
    }
    
    /**
     * Synchronize student schedules when teacher changes schedule
     */
    private void synchronizeStudentSchedules(CourseSchedule originalSchedule, CourseSchedule updatedSchedule) {
        try {
            System.out.println("🔄 Synchronizing student schedules for course: " + updatedSchedule.getCourseId());

            // Get all students enrolled in this course
            Course course = courseService.getCourseById(updatedSchedule.getCourseId());
            if (course == null || course.getEnrolledStudents() == null) {
                System.out.println("⚠️ No enrolled students found for course: " + updatedSchedule.getCourseId());
                return;
            }

            List<String> enrolledStudents = course.getEnrolledStudents();
            System.out.println("👥 Found " + enrolledStudents.size() + " enrolled students");

            // Update schedule for each enrolled student
            for (String studentId : enrolledStudents) {
                updateStudentSchedule(studentId, originalSchedule, updatedSchedule);
            }

            System.out.println("✅ Student schedule synchronization completed");

        } catch (Exception e) {
            System.err.println("❌ Error synchronizing student schedules: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Update schedule for a specific student
     */
    private void updateStudentSchedule(String studentId, CourseSchedule originalSchedule, CourseSchedule updatedSchedule) {
        try {
            // Get student's existing schedules
            List<CourseSchedule> studentSchedules = scheduleService.getSchedulesByStudent(studentId);

            // Find the schedule entry that matches the original schedule
            CourseSchedule studentScheduleToUpdate = studentSchedules.stream()
                    .filter(schedule -> schedule.getCourseId().equals(originalSchedule.getCourseId()) &&
                                      schedule.getDayOfWeek() == originalSchedule.getDayOfWeek() &&
                                      schedule.getStartTime().equals(originalSchedule.getStartTime()) &&
                                      schedule.getEndTime().equals(originalSchedule.getEndTime()))
                    .findFirst()
                    .orElse(null);

            if (studentScheduleToUpdate != null) {
                // Update the student's schedule with new information
                studentScheduleToUpdate.setDayOfWeek(updatedSchedule.getDayOfWeek());
                studentScheduleToUpdate.setStartTime(updatedSchedule.getStartTime());
                studentScheduleToUpdate.setEndTime(updatedSchedule.getEndTime());
                studentScheduleToUpdate.setClassroom(updatedSchedule.getClassroom());
                studentScheduleToUpdate.setWeekType(updatedSchedule.getWeekType());
                studentScheduleToUpdate.setActive(updatedSchedule.isActive());

                // Save the updated student schedule
                boolean success = scheduleService.updateSchedule(studentScheduleToUpdate);
                if (success) {
                    System.out.println("✅ Updated schedule for student: " + studentId);
                } else {
                    System.err.println("❌ Failed to update schedule for student: " + studentId);
                }
            } else {
                System.out.println("⚠️ No matching schedule found for student: " + studentId);
            }

        } catch (Exception e) {
            System.err.println("❌ Error updating schedule for student " + studentId + ": " + e.getMessage());
        }
    }

    /**
     * Update the course's schedule string for backward compatibility
     */
    private void updateCourseScheduleString(CourseSchedule updatedSchedule) {
        try {
            Course course = courseService.getCourseById(updatedSchedule.getCourseId());
            if (course != null) {
                // Create new schedule string
                String newScheduleString = updatedSchedule.getDayOfWeekText() + " - " +
                                         updatedSchedule.getTimeRange();

                // Update course schedule
                course.setSchedule(newScheduleString);
                course.setClassroom(updatedSchedule.getClassroom());

                // Save updated course
                boolean success = courseService.updateCourse(course);
                if (success) {
                    System.out.println("✅ Updated course schedule string: " + newScheduleString);
                } else {
                    System.err.println("❌ Failed to update course schedule string");
                }
            }

        } catch (Exception e) {
            System.err.println("❌ Error updating course schedule string: " + e.getMessage());
        }
    }

    private void closeDialog() {
        Stage stage = (Stage) saveBtn.getScene().getWindow();
        stage.close();
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    public boolean isSaved() {
        return isSaved;
    }
}
