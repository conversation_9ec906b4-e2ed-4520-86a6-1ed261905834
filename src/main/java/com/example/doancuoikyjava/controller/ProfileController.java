package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.service.*;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.image.ImageView;
import javafx.scene.layout.GridPane;
import javafx.stage.Stage;
import com.example.doancuoikyjava.util.AvatarUtils;

import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;

public class ProfileController implements Initializable {
    
    @FXML private Label welcomeLabel;
    @FXML private Button backButton;
    @FXML private Button editButton;
    @FXML private Button saveButton;
    @FXML private Button cancelButton;

    // Avatar
    @FXML private ImageView avatarImageView;
    @FXML private Button changeAvatarButton;
    
    // Common fields
    @FXML private TextField userIdField;
    @FXML private TextField fullNameField;
    @FXML private TextField emailField;
    @FXML private TextField phoneField;
    @FXML private TextArea addressArea;
    @FXML private DatePicker birthDatePicker;

    
    // Student specific fields
    @FXML private TextField classNameField;
    @FXML private TextField majorField;
    @FXML private TextField gpaField;
    @FXML private TextField enrollmentYearField;
    
    // Teacher specific fields
    @FXML private TextField departmentField;
    @FXML private TextField positionField;
    @FXML private TextField experienceField;
    @FXML private TextField salaryField;
    
    // Labels for sections
    @FXML private Label studentSectionLabel;
    @FXML private Label teacherSectionLabel;
    
    private UserService userService;
    private User currentUser;
    private boolean isEditMode = false;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        try {
            System.out.println("ProfileController: Bắt đầu initialize");
            userService = new UserService();
            currentUser = SceneManager.getCurrentUser();

            if (currentUser == null) {
                System.out.println("ProfileController: currentUser is null!");
                showAlert("Lỗi", "Không thể lấy thông tin người dùng hiện tại!");
                return;
            }

            System.out.println("ProfileController: currentUser = " + currentUser.getFullName());

            setupUI();
            loadUserData();
            loadAvatar();
            setEditMode(false);

            System.out.println("ProfileController: Initialize thành công");
        } catch (Exception e) {
            System.out.println("ProfileController: Lỗi initialize: " + e.getMessage());
            e.printStackTrace();
            showAlert("Lỗi", "Không thể khởi tạo trang thông tin cá nhân: " + e.getMessage());
        }
    }
    
    private void setupUI() {
        if (currentUser != null) {
            welcomeLabel.setText("Thông tin cá nhân - " + currentUser.getFullName());
        }
        

        
        // Show/hide sections based on user type
        if (currentUser instanceof Student) {
            studentSectionLabel.setVisible(true);
            classNameField.setVisible(true);
            majorField.setVisible(true);
            gpaField.setVisible(true);
            enrollmentYearField.setVisible(true);
            
            teacherSectionLabel.setVisible(false);
            departmentField.setVisible(false);
            positionField.setVisible(false);
            experienceField.setVisible(false);
            salaryField.setVisible(false);
        } else if (currentUser instanceof Teacher) {
            studentSectionLabel.setVisible(false);
            classNameField.setVisible(false);
            majorField.setVisible(false);
            gpaField.setVisible(false);
            enrollmentYearField.setVisible(false);
            
            teacherSectionLabel.setVisible(true);
            departmentField.setVisible(true);
            positionField.setVisible(true);
            experienceField.setVisible(true);
            salaryField.setVisible(true);
        }
    }
    
    private void loadUserData() {
        if (currentUser == null) return;
        
        // Load common data
        userIdField.setText(currentUser.getUserId());
        fullNameField.setText(currentUser.getFullName());
        emailField.setText(currentUser.getEmail());
        phoneField.setText(currentUser.getPhone());
        addressArea.setText(currentUser.getAddress());
        
        if (currentUser.getDateOfBirth() != null) {
            birthDatePicker.setValue(currentUser.getDateOfBirth());
        }

        // Gender is not in User model, so skip this
        
        // Load specific data based on user type
        if (currentUser instanceof Student) {
            Student student = (Student) currentUser;
            classNameField.setText(student.getClassName());
            majorField.setText(student.getMajor());
            gpaField.setText(String.format("%.2f", student.getGpa()));
            enrollmentYearField.setText(String.valueOf(student.getYear()));
        } else if (currentUser instanceof Teacher) {
            Teacher teacher = (Teacher) currentUser;
            departmentField.setText(teacher.getDepartment());
            positionField.setText(teacher.getPosition());
            experienceField.setText(String.valueOf(teacher.getExperienceYears()));
            salaryField.setText(String.format("%.0f", teacher.getSalary()));
        }
    }
    
    private void setEditMode(boolean editMode) {
        isEditMode = editMode;
        
        // Enable/disable fields
        fullNameField.setEditable(editMode);
        emailField.setEditable(editMode);
        phoneField.setEditable(editMode);
        addressArea.setEditable(editMode);
        birthDatePicker.setDisable(!editMode);
        
        if (currentUser instanceof Student) {
            classNameField.setEditable(editMode);
            majorField.setEditable(editMode);
            // GPA and enrollment year should not be editable by student
            gpaField.setEditable(false);
            enrollmentYearField.setEditable(false);
        } else if (currentUser instanceof Teacher) {
            departmentField.setEditable(editMode);
            positionField.setEditable(editMode);
            experienceField.setEditable(editMode);
            // Salary should not be editable by teacher
            salaryField.setEditable(false);
        }
        
        // Show/hide buttons
        editButton.setVisible(!editMode);
        saveButton.setVisible(editMode);
        cancelButton.setVisible(editMode);
        changeAvatarButton.setVisible(editMode);

        // User ID should never be editable
        userIdField.setEditable(false);
    }
    
    @FXML
    private void editProfile() {
        setEditMode(true);
    }
    
    @FXML
    private void saveProfile() {
        try {
            // Validate input
            if (fullNameField.getText().trim().isEmpty()) {
                showAlert("Lỗi", "Họ tên không được để trống!");
                return;
            }
            
            if (emailField.getText().trim().isEmpty()) {
                showAlert("Lỗi", "Email không được để trống!");
                return;
            }
            
            // Update user data
            currentUser.setFullName(fullNameField.getText().trim());
            currentUser.setEmail(emailField.getText().trim());
            currentUser.setPhone(phoneField.getText().trim());
            currentUser.setAddress(addressArea.getText().trim());
            currentUser.setDateOfBirth(birthDatePicker.getValue());
            // Skip gender as it's not in User model
            
            // Update specific data based on user type
            if (currentUser instanceof Student) {
                Student student = (Student) currentUser;
                student.setClassName(classNameField.getText().trim());
                student.setMajor(majorField.getText().trim());
            } else if (currentUser instanceof Teacher) {
                Teacher teacher = (Teacher) currentUser;
                teacher.setDepartment(departmentField.getText().trim());
                teacher.setPosition(positionField.getText().trim());
                
                try {
                    int experience = Integer.parseInt(experienceField.getText().trim());
                    teacher.setExperienceYears(experience);
                } catch (NumberFormatException e) {
                    showAlert("Lỗi", "Kinh nghiệm phải là số nguyên!");
                    return;
                }
            }
            
            // Save to database/file
            boolean success = userService.updateUser(currentUser);
            
            if (success) {
                showAlert("Thành công", "Cập nhật thông tin cá nhân thành công!");
                setEditMode(false);
                
                // Update current user in SceneManager
                SceneManager.setCurrentUser(currentUser);
            } else {
                showAlert("Lỗi", "Không thể cập nhật thông tin. Vui lòng thử lại!");
            }
            
        } catch (Exception e) {
            showAlert("Lỗi", "Có lỗi xảy ra: " + e.getMessage());
        }
    }
    
    @FXML
    private void cancelEdit() {
        loadUserData(); // Reload original data
        loadAvatar(); // Reload original avatar
        setEditMode(false);
    }

    private void loadAvatar() {
        try {
            if (currentUser != null && avatarImageView != null) {
                System.out.println("ProfileController: Loading avatar for " + currentUser.getUserId());
                AvatarUtils.setupAvatarImageView(avatarImageView, currentUser.getAvatarPath(), 120, 120);
                System.out.println("ProfileController: Avatar loaded successfully");
            } else {
                System.out.println("ProfileController: Cannot load avatar - currentUser=" + currentUser + ", avatarImageView=" + avatarImageView);
            }
        } catch (Exception e) {
            System.out.println("ProfileController: Lỗi load avatar: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @FXML
    private void changeAvatar() {
        try {
            Stage stage = (Stage) changeAvatarButton.getScene().getWindow();
            java.io.File selectedFile = AvatarUtils.chooseAvatarFile(stage);

            if (selectedFile != null) {
                // Kiểm tra file có phải là ảnh không
                if (!AvatarUtils.isImageFile(selectedFile)) {
                    showAlert("Lỗi", "Vui lòng chọn file ảnh (PNG, JPG, JPEG, GIF, BMP)!");
                    return;
                }

                // Kiểm tra kích thước file (giới hạn 5MB)
                if (!AvatarUtils.isValidFileSize(selectedFile)) {
                    showAlert("Lỗi", "Kích thước file không được vượt quá 5MB!");
                    return;
                }

                // Lưu avatar mới
                String newAvatarPath = AvatarUtils.saveAvatar(selectedFile, currentUser.getUserId());

                if (newAvatarPath != null) {
                    // Xóa avatar cũ nếu có
                    if (currentUser.getAvatarPath() != null) {
                        AvatarUtils.deleteAvatar(currentUser.getAvatarPath());
                    }

                    // Cập nhật avatar path
                    currentUser.setAvatarPath(newAvatarPath);

                    // Hiển thị avatar mới
                    loadAvatar();

                    showAlert("Thành công", "Đã cập nhật ảnh đại diện!");
                } else {
                    showAlert("Lỗi", "Không thể lưu ảnh đại diện. Vui lòng thử lại!");
                }
            }
        } catch (Exception e) {
            showAlert("Lỗi", "Có lỗi xảy ra khi thay đổi ảnh đại diện: " + e.getMessage());
        }
    }
    
    @FXML
    private void changePassword() {
        // Create password change dialog
        Dialog<String> dialog = new Dialog<>();
        dialog.setTitle("Đổi mật khẩu");
        dialog.setHeaderText("Thay đổi mật khẩu của bạn");
        
        // Set button types
        ButtonType changeButtonType = new ButtonType("Đổi mật khẩu", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(changeButtonType, ButtonType.CANCEL);
        
        // Create password fields
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new javafx.geometry.Insets(20, 150, 10, 10));
        
        PasswordField currentPassword = new PasswordField();
        currentPassword.setPromptText("Mật khẩu hiện tại");
        PasswordField newPassword = new PasswordField();
        newPassword.setPromptText("Mật khẩu mới");
        PasswordField confirmPassword = new PasswordField();
        confirmPassword.setPromptText("Xác nhận mật khẩu mới");
        
        grid.add(new Label("Mật khẩu hiện tại:"), 0, 0);
        grid.add(currentPassword, 1, 0);
        grid.add(new Label("Mật khẩu mới:"), 0, 1);
        grid.add(newPassword, 1, 1);
        grid.add(new Label("Xác nhận mật khẩu:"), 0, 2);
        grid.add(confirmPassword, 1, 2);
        
        dialog.getDialogPane().setContent(grid);
        
        // Convert result when change button is clicked
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == changeButtonType) {
                if (!currentPassword.getText().equals(currentUser.getPassword())) {
                    showAlert("Lỗi", "Mật khẩu hiện tại không đúng!");
                    return null;
                }
                
                if (newPassword.getText().length() < 6) {
                    showAlert("Lỗi", "Mật khẩu mới phải có ít nhất 6 ký tự!");
                    return null;
                }
                
                if (!newPassword.getText().equals(confirmPassword.getText())) {
                    showAlert("Lỗi", "Mật khẩu xác nhận không khớp!");
                    return null;
                }
                
                return newPassword.getText();
            }
            return null;
        });
        
        dialog.showAndWait().ifPresent(newPass -> {
            currentUser.setPassword(newPass);
            boolean success = userService.updateUser(currentUser);
            
            if (success) {
                showAlert("Thành công", "Đổi mật khẩu thành công!");
            } else {
                showAlert("Lỗi", "Không thể đổi mật khẩu. Vui lòng thử lại!");
            }
        });
    }
    
    @FXML
    private void goBack() {
        try {
            if (currentUser instanceof Student) {
                SceneManager.switchScene("/com/example/doancuoikyjava/student-dashboard.fxml", 
                                       "Sinh viên - " + currentUser.getFullName());
            } else if (currentUser instanceof Teacher) {
                SceneManager.switchScene("/com/example/doancuoikyjava/teacher-dashboard.fxml", 
                                       "Giáo viên - " + currentUser.getFullName());
            }
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại trang chính: " + e.getMessage());
        }
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
