package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.service.*;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

import java.net.URL;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;
import java.util.stream.IntStream;
import java.util.stream.Collectors;

/**
 * Controller for Add/Edit Schedule Dialog
 */
public class AddScheduleDialogController implements Initializable {
    
    @FXML private Label titleLabel;
    @FXML private ComboBox<Course> courseComboBox;
    @FXML private Label courseInfoLabel;
    @FXML private ComboBox<Teacher> teacherComboBox;
    @FXML private Label teacherInfoLabel;
    @FXML private ComboBox<String> dayComboBox;
    @FXML private ComboBox<String> startTimeComboBox;
    @FXML private ComboBox<String> endTimeComboBox;
    @FXML private ComboBox<String> classroomComboBox;
    @FXML private ComboBox<String> weekTypeComboBox;
    @FXML private ComboBox<String> semesterComboBox;
    @FXML private ComboBox<String> academicYearComboBox;
    @FXML private VBox conflictCheckBox;
    @FXML private Label conflictResultLabel;
    @FXML private Label previewLabel;
    @FXML private Label previewTeacherLabel;
    @FXML private TextArea notesTextArea;
    @FXML private Button checkConflictBtn;
    @FXML private Button saveBtn;
    
    private ScheduleService scheduleService;
    private CourseService courseService;
    private DatabaseUserService userService;
    private CourseSchedule editingSchedule; // null for new schedule
    private boolean isSaved = false;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("🔧 AddScheduleDialogController: Initializing...");
        
        try {
            scheduleService = new ScheduleService();
            courseService = new CourseService();
            userService = new DatabaseUserService();

            setupComboBoxes();
            setupEventHandlers();

            System.out.println("✅ AddScheduleDialogController: Initialization completed");

        } catch (Exception e) {
            System.err.println("❌ AddScheduleDialogController: Initialization failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void setupComboBoxes() {
        // Setup courses and teachers
        setupCourseComboBox();
        setupTeacherComboBox();

        // Setup days of week
        dayComboBox.setItems(FXCollections.observableArrayList(
            "Thứ 2", "Thứ 3", "Thứ 4", "Thứ 5", "Thứ 6", "Thứ 7", "Chủ nhật"
        ));
        
        // Setup time slots (every 15 minutes from 7:00 to 18:00)
        setupTimeComboBoxes();
        
        // Setup classrooms (1-50)
        ObservableList<String> classrooms = FXCollections.observableArrayList();
        IntStream.rangeClosed(1, 50).forEach(i -> classrooms.add("Phòng " + i));
        
        // Add special classrooms
        classrooms.addAll(
            "Lab A101", "Lab A102", "Lab B201", "Lab B202", 
            "Lab C101", "Lab C102", "Lab AI01", "Lab AI02", 
            "Lab Data01", "Sân thể thao", "Hội trường A", "Hội trường B"
        );
        
        classroomComboBox.setItems(classrooms);
        
        // Setup week types
        weekTypeComboBox.setItems(FXCollections.observableArrayList(
            "Tất cả tuần", "Tuần lẻ", "Tuần chẵn"
        ));
        weekTypeComboBox.setValue("Tất cả tuần");
        
        // Setup semesters
        semesterComboBox.setItems(FXCollections.observableArrayList(
            "HK1 2024-2025", "HK2 2024-2025", "HK3 2024-2025",
            "HK1 2025-2026", "HK2 2025-2026", "HK3 2025-2026"
        ));
        semesterComboBox.setValue("HK1 2024-2025");
        
        // Setup academic years
        academicYearComboBox.setItems(FXCollections.observableArrayList(
            "2024-2025", "2025-2026", "2026-2027"
        ));
        academicYearComboBox.setValue("2024-2025");
    }
    
    private void setupCourseComboBox() {
        try {
            List<Course> courses = courseService.getAllCourses();
            ObservableList<Course> courseList = FXCollections.observableArrayList(courses);
            courseComboBox.setItems(courseList);
            
            // Custom cell factory to display course name and teacher
            courseComboBox.setCellFactory(listView -> new ListCell<Course>() {
                @Override
                protected void updateItem(Course course, boolean empty) {
                    super.updateItem(course, empty);
                    if (empty || course == null) {
                        setText(null);
                    } else {
                        setText(course.getCourseName() + " - " + 
                               (course.getTeacherName() != null ? course.getTeacherName() : "Chưa có GV"));
                    }
                }
            });
            
            courseComboBox.setButtonCell(new ListCell<Course>() {
                @Override
                protected void updateItem(Course course, boolean empty) {
                    super.updateItem(course, empty);
                    if (empty || course == null) {
                        setText("Chọn môn học");
                    } else {
                        setText(course.getCourseName());
                    }
                }
            });
            
        } catch (Exception e) {
            System.err.println("❌ Error loading courses: " + e.getMessage());
        }
    }

    private void setupTeacherComboBox() {
        try {
            List<User> users = userService.getUsersByRole(User.UserRole.TEACHER);
            List<Teacher> teachers = users.stream()
                .filter(user -> user instanceof Teacher)
                .map(user -> (Teacher) user)
                .collect(Collectors.toList());

            ObservableList<Teacher> teacherList = FXCollections.observableArrayList(teachers);
            teacherComboBox.setItems(teacherList);

            // Custom cell factory to display teacher name and department
            teacherComboBox.setCellFactory(listView -> new ListCell<Teacher>() {
                @Override
                protected void updateItem(Teacher teacher, boolean empty) {
                    super.updateItem(teacher, empty);
                    if (empty || teacher == null) {
                        setText(null);
                    } else {
                        setText(teacher.getFullName() + " - " +
                               (teacher.getDepartment() != null ? teacher.getDepartment() : "N/A"));
                    }
                }
            });

            teacherComboBox.setButtonCell(new ListCell<Teacher>() {
                @Override
                protected void updateItem(Teacher teacher, boolean empty) {
                    super.updateItem(teacher, empty);
                    if (empty || teacher == null) {
                        setText("Chọn giảng viên");
                    } else {
                        setText(teacher.getFullName());
                    }
                }
            });

        } catch (Exception e) {
            System.err.println("❌ Error loading teachers: " + e.getMessage());
        }
    }
    
    private void setupTimeComboBoxes() {
        ObservableList<String> timeSlots = FXCollections.observableArrayList();
        
        // Generate time slots from 7:00 to 18:00 every 15 minutes
        LocalTime start = LocalTime.of(7, 0);
        LocalTime end = LocalTime.of(18, 0);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        
        LocalTime current = start;
        while (!current.isAfter(end)) {
            timeSlots.add(current.format(formatter));
            current = current.plusMinutes(15);
        }
        
        startTimeComboBox.setItems(timeSlots);
        endTimeComboBox.setItems(timeSlots);
    }
    
    private void setupEventHandlers() {
        // Update course info when course is selected
        courseComboBox.setOnAction(e -> updateCourseInfo());

        // Update teacher info when teacher is selected
        teacherComboBox.setOnAction(e -> updateTeacherInfo());

        // Update preview when any field changes
        dayComboBox.setOnAction(e -> updatePreview());
        startTimeComboBox.setOnAction(e -> updatePreview());
        endTimeComboBox.setOnAction(e -> updatePreview());
        classroomComboBox.setOnAction(e -> updatePreview());
        weekTypeComboBox.setOnAction(e -> updatePreview());
        teacherComboBox.setOnAction(e -> updatePreview());
        
        // Auto-check conflicts when time or classroom changes
        startTimeComboBox.setOnAction(e -> autoCheckConflicts());
        endTimeComboBox.setOnAction(e -> autoCheckConflicts());
        classroomComboBox.setOnAction(e -> autoCheckConflicts());
        dayComboBox.setOnAction(e -> autoCheckConflicts());
    }
    
    private void updateCourseInfo() {
        Course selectedCourse = courseComboBox.getValue();
        if (selectedCourse != null) {
            String info = String.format("Giảng viên: %s | Tín chỉ: %d | Sinh viên: %d/%d",
                selectedCourse.getTeacherName() != null ? selectedCourse.getTeacherName() : "Chưa có",
                selectedCourse.getCredits(),
                selectedCourse.getEnrolledStudents().size(),
                selectedCourse.getMaxStudents()
            );
            courseInfoLabel.setText(info);
        } else {
            courseInfoLabel.setText("");
        }
        updatePreview();
    }

    private void updateTeacherInfo() {
        Teacher selectedTeacher = teacherComboBox.getValue();
        if (selectedTeacher != null) {
            String info = String.format("Khoa: %s | Chức vụ: %s | Kinh nghiệm: %d năm",
                selectedTeacher.getDepartment() != null ? selectedTeacher.getDepartment() : "N/A",
                selectedTeacher.getPosition() != null ? selectedTeacher.getPosition() : "N/A",
                selectedTeacher.getExperienceYears()
            );
            teacherInfoLabel.setText(info);
        } else {
            teacherInfoLabel.setText("");
        }
        updatePreview();
    }
    
    private void updatePreview() {
        Course course = courseComboBox.getValue();
        Teacher teacher = teacherComboBox.getValue();
        String day = dayComboBox.getValue();
        String startTime = startTimeComboBox.getValue();
        String endTime = endTimeComboBox.getValue();
        String classroom = classroomComboBox.getValue();
        String weekType = weekTypeComboBox.getValue();

        if (course != null && day != null && startTime != null && endTime != null && classroom != null) {
            String preview = String.format(
                "📚 %s\n" +
                "🕒 %s, %s - %s\n" +
                "🏫 %s\n" +
                "📅 %s (%s)",
                course.getCourseName(),
                day, startTime, endTime,
                classroom,
                semesterComboBox.getValue(),
                weekType != null ? weekType : "Tất cả tuần"
            );
            previewLabel.setText(preview);

            // Update teacher preview
            if (teacher != null) {
                previewTeacherLabel.setText("👨‍🏫 Giảng viên: " + teacher.getFullName());
            } else {
                previewTeacherLabel.setText("⚠️ Chưa chọn giảng viên");
            }
        } else {
            previewLabel.setText("Vui lòng điền đầy đủ thông tin để xem trước");
            previewTeacherLabel.setText("");
        }
    }
    
    private void autoCheckConflicts() {
        // Auto check conflicts when key fields change
        if (isFormValid()) {
            checkConflicts();
        }
    }
    
    @FXML
    private void checkConflicts() {
        if (!isFormValid()) {
            showAlert("Lỗi", "Vui lòng điền đầy đủ thông tin bắt buộc!");
            return;
        }
        
        try {
            CourseSchedule tempSchedule = createScheduleFromForm();
            boolean hasConflict = scheduleService.hasScheduleConflict(tempSchedule);
            
            conflictCheckBox.setVisible(true);
            
            if (hasConflict) {
                conflictResultLabel.setText("❌ PHÁT HIỆN XUNG ĐỘT!\n" +
                    "Đã có lịch học khác trong cùng thời gian và phòng học này.\n" +
                    "Vui lòng chọn thời gian hoặc phòng học khác.");
                conflictCheckBox.setStyle("-fx-background-color: #FFEBEE; -fx-border-color: #F44336;");
                saveBtn.setDisable(true);
            } else {
                conflictResultLabel.setText("✅ KHÔNG CÓ XUNG ĐỘT!\n" +
                    "Lịch học này có thể được tạo an toàn.");
                conflictCheckBox.setStyle("-fx-background-color: #E8F5E8; -fx-border-color: #4CAF50;");
                saveBtn.setDisable(false);
            }
            
        } catch (Exception e) {
            System.err.println("❌ Error checking conflicts: " + e.getMessage());
            showAlert("Lỗi", "Không thể kiểm tra xung đột: " + e.getMessage());
        }
    }
    
    @FXML
    private void save() {
        if (!isFormValid()) {
            showAlert("Lỗi", "Vui lòng điền đầy đủ thông tin bắt buộc!");
            return;
        }
        
        try {
            CourseSchedule schedule = createScheduleFromForm();
            
            boolean success;
            if (editingSchedule == null) {
                // Adding new schedule
                success = scheduleService.addSchedule(schedule);
            } else {
                // Updating existing schedule
                schedule.setScheduleId(editingSchedule.getScheduleId());
                success = scheduleService.updateSchedule(schedule);
            }
            
            if (success) {
                isSaved = true;
                showAlert("Thành công", 
                    editingSchedule == null ? "Đã thêm lịch học mới!" : "Đã cập nhật lịch học!");
                closeDialog();
            } else {
                showAlert("Lỗi", "Không thể lưu lịch học. Vui lòng thử lại!");
            }
            
        } catch (Exception e) {
            System.err.println("❌ Error saving schedule: " + e.getMessage());
            showAlert("Lỗi", "Lỗi khi lưu lịch học: " + e.getMessage());
        }
    }
    
    @FXML
    private void cancel() {
        closeDialog();
    }
    
    private boolean isFormValid() {
        return courseComboBox.getValue() != null &&
               teacherComboBox.getValue() != null &&
               dayComboBox.getValue() != null &&
               startTimeComboBox.getValue() != null &&
               endTimeComboBox.getValue() != null &&
               classroomComboBox.getValue() != null;
    }
    
    private CourseSchedule createScheduleFromForm() {
        CourseSchedule schedule = new CourseSchedule();

        Course selectedCourse = courseComboBox.getValue();
        schedule.setCourseId(selectedCourse.getCourseId());
        schedule.setCourseName(selectedCourse.getCourseName());

        Teacher selectedTeacher = teacherComboBox.getValue();
        if (selectedTeacher != null) {
            schedule.setTeacherId(selectedTeacher.getTeacherId());
            schedule.setTeacherName(selectedTeacher.getFullName());
        }
        
        // Convert day text to number
        String dayText = dayComboBox.getValue();
        int dayOfWeek = switch (dayText) {
            case "Thứ 2" -> 2;
            case "Thứ 3" -> 3;
            case "Thứ 4" -> 4;
            case "Thứ 5" -> 5;
            case "Thứ 6" -> 6;
            case "Thứ 7" -> 7;
            case "Chủ nhật" -> 8;
            default -> 2;
        };
        schedule.setDayOfWeek(dayOfWeek);
        
        schedule.setStartTime(LocalTime.parse(startTimeComboBox.getValue()));
        schedule.setEndTime(LocalTime.parse(endTimeComboBox.getValue()));
        schedule.setClassroom(classroomComboBox.getValue());
        
        // Convert week type
        String weekTypeText = weekTypeComboBox.getValue();
        String weekType = switch (weekTypeText) {
            case "Tuần lẻ" -> "ODD";
            case "Tuần chẵn" -> "EVEN";
            default -> "ALL";
        };
        schedule.setWeekType(weekType);
        
        schedule.setSemester(semesterComboBox.getValue());
        schedule.setAcademicYear(academicYearComboBox.getValue());
        schedule.setActive(true);
        
        return schedule;
    }
    
    public void setEditingSchedule(CourseSchedule schedule) {
        this.editingSchedule = schedule;
        if (schedule != null) {
            titleLabel.setText("CHỈNH SỬA LỊCH HỌC");
            populateFormWithSchedule(schedule);
        }
    }
    
    private void populateFormWithSchedule(CourseSchedule schedule) {
        // Find and select course
        Course course = courseService.getCourseById(schedule.getCourseId());
        if (course != null) {
            courseComboBox.setValue(course);
        }

        // Find and select teacher
        if (schedule.getTeacherId() != null) {
            Teacher teacher = teacherComboBox.getItems().stream()
                .filter(t -> t.getTeacherId().equals(schedule.getTeacherId()))
                .findFirst()
                .orElse(null);
            if (teacher != null) {
                teacherComboBox.setValue(teacher);
            }
        }

        // Set day
        dayComboBox.setValue(schedule.getDayOfWeekText());
        
        // Set times
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        startTimeComboBox.setValue(schedule.getStartTime().format(formatter));
        endTimeComboBox.setValue(schedule.getEndTime().format(formatter));
        
        // Set classroom
        classroomComboBox.setValue(schedule.getClassroom());
        
        // Set week type
        String weekTypeText = switch (schedule.getWeekType()) {
            case "ODD" -> "Tuần lẻ";
            case "EVEN" -> "Tuần chẵn";
            default -> "Tất cả tuần";
        };
        weekTypeComboBox.setValue(weekTypeText);
        
        semesterComboBox.setValue(schedule.getSemester());
        academicYearComboBox.setValue(schedule.getAcademicYear());
    }
    
    public boolean isSaved() {
        return isSaved;
    }
    
    private void closeDialog() {
        Stage stage = (Stage) saveBtn.getScene().getWindow();
        stage.close();
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
