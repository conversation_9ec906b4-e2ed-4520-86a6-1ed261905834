package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.Notification;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.NotificationService;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.HBox;

import java.io.IOException;
import java.net.URL;
import java.util.List;
import java.util.ResourceBundle;

public class NotificationViewController implements Initializable {
    
    @FXML private Label welcomeLabel;
    @FXML private Button backButton;
    @FXML private Button refreshButton;
    
    // Statistics
    @FXML private Label totalNotificationsLabel;
    @FXML private Label unreadNotificationsLabel;
    @FXML private Label urgentNotificationsLabel;
    @FXML private Label todayNotificationsLabel;
    
    // Filters
    @FXML private ComboBox<String> typeFilterComboBox;
    @FXML private ComboBox<String> priorityFilterComboBox;
    @FXML private ComboBox<String> statusFilterComboBox;
    @FXML private TextField searchField;
    
    // Table
    @FXML private TableView<Notification> notificationsTableView;
    @FXML private TableColumn<Notification, String> titleColumn;
    @FXML private TableColumn<Notification, String> typeColumn;
    @FXML private TableColumn<Notification, String> priorityColumn;
    @FXML private TableColumn<Notification, String> statusColumn;
    @FXML private TableColumn<Notification, String> createdAtColumn;
    @FXML private TableColumn<Notification, String> expiryColumn;
    @FXML private TableColumn<Notification, Void> actionsColumn;
    
    private NotificationService notificationService;
    private ObservableList<Notification> allNotifications;
    private ObservableList<Notification> filteredNotifications;
    private User currentUser;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        notificationService = new NotificationService();
        allNotifications = FXCollections.observableArrayList();
        filteredNotifications = FXCollections.observableArrayList();
        currentUser = SceneManager.getCurrentUser();
        
        setupUI();
        setupTableColumns();
        setupFilters();
        loadNotifications();
    }
    
    private void setupUI() {
        String userType = currentUser.getRole() == User.UserRole.STUDENT ? "Sinh viên" : "Giảng viên";
        welcomeLabel.setText("📢 Thông báo - " + userType + " " + currentUser.getFullName());
    }
    
    private void setupTableColumns() {
        titleColumn.setCellValueFactory(new PropertyValueFactory<>("title"));
        
        typeColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getTypeIcon() + " " + getTypeDisplayName(cellData.getValue().getType())));
        
        priorityColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getPriorityIcon() + " " + getPriorityDisplayName(cellData.getValue().getPriority())));
        
        statusColumn.setCellValueFactory(cellData -> {
            Notification notification = cellData.getValue();
            String status = notification.isExpired() ? "Đã hết hạn" : "Còn hiệu lực";
            return new SimpleStringProperty(status);
        });
        
        createdAtColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getFormattedCreatedAt()));
        
        expiryColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getFormattedExpiryDate()));
        
        // Actions column
        actionsColumn.setCellFactory(param -> new TableCell<Notification, Void>() {
            private final Button viewBtn = new Button("👁️ Xem");
            private final Button markReadBtn = new Button("✅ Đã đọc");
            private final HBox buttonBox = new HBox(5);
            
            {
                viewBtn.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-size: 11px; -fx-padding: 5 10;");
                markReadBtn.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-size: 11px; -fx-padding: 5 10;");
                
                viewBtn.setOnAction(e -> {
                    Notification notification = getTableView().getItems().get(getIndex());
                    viewNotificationDetail(notification);
                });
                
                markReadBtn.setOnAction(e -> {
                    Notification notification = getTableView().getItems().get(getIndex());
                    markAsRead(notification);
                });
                
                buttonBox.getChildren().addAll(viewBtn, markReadBtn);
            }
            
            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(buttonBox);
                }
            }
        });
        
        notificationsTableView.setItems(filteredNotifications);
    }
    
    private void setupFilters() {
        // Type filter
        typeFilterComboBox.getItems().addAll(
            "Tất cả loại", "Thông báo chung", "Học tập", "Thi cử", "Sự kiện", "Khẩn cấp"
        );
        typeFilterComboBox.setValue("Tất cả loại");
        
        // Priority filter
        priorityFilterComboBox.getItems().addAll(
            "Tất cả mức độ", "Thấp", "Bình thường", "Cao", "Khẩn cấp"
        );
        priorityFilterComboBox.setValue("Tất cả mức độ");
        
        // Status filter
        statusFilterComboBox.getItems().addAll(
            "Tất cả trạng thái", "Còn hiệu lực", "Đã hết hạn"
        );
        statusFilterComboBox.setValue("Tất cả trạng thái");
        
        // Search functionality
        searchField.textProperty().addListener((observable, oldValue, newValue) -> filterNotifications());
        typeFilterComboBox.setOnAction(e -> filterNotifications());
        priorityFilterComboBox.setOnAction(e -> filterNotifications());
        statusFilterComboBox.setOnAction(e -> filterNotifications());
    }
    
    private void loadNotifications() {
        List<Notification> notifications = notificationService.getNotificationsByTarget(currentUser.getRole());
        allNotifications.setAll(notifications);
        filterNotifications();
        updateStatistics();
    }
    
    private void filterNotifications() {
        String searchText = searchField.getText().toLowerCase();
        String selectedType = typeFilterComboBox.getValue();
        String selectedPriority = priorityFilterComboBox.getValue();
        String selectedStatus = statusFilterComboBox.getValue();
        
        List<Notification> filtered = allNotifications.stream()
                .filter(notification -> {
                    boolean matchesSearch = searchText.isEmpty() || 
                            notification.getTitle().toLowerCase().contains(searchText) ||
                            notification.getContent().toLowerCase().contains(searchText);
                    
                    boolean matchesType = selectedType == null || selectedType.equals("Tất cả loại") ||
                            getTypeDisplayName(notification.getType()).equals(selectedType);
                    
                    boolean matchesPriority = selectedPriority == null || selectedPriority.equals("Tất cả mức độ") ||
                            getPriorityDisplayName(notification.getPriority()).equals(selectedPriority);
                    
                    boolean matchesStatus = selectedStatus == null || selectedStatus.equals("Tất cả trạng thái") ||
                            (selectedStatus.equals("Còn hiệu lực") && !notification.isExpired()) ||
                            (selectedStatus.equals("Đã hết hạn") && notification.isExpired());
                    
                    return matchesSearch && matchesType && matchesPriority && matchesStatus;
                })
                .toList();
        
        filteredNotifications.setAll(filtered);
    }
    
    private void updateStatistics() {
        int total = allNotifications.size();
        long urgent = allNotifications.stream()
                .filter(n -> n.getPriority() == Notification.Priority.URGENT).count();
        long today = allNotifications.stream()
                .filter(n -> n.getCreatedAt().toLocalDate().equals(java.time.LocalDate.now())).count();
        
        totalNotificationsLabel.setText(String.valueOf(total));
        unreadNotificationsLabel.setText(String.valueOf(total)); // Simplified - all are "unread"
        urgentNotificationsLabel.setText(String.valueOf(urgent));
        todayNotificationsLabel.setText(String.valueOf(today));
    }
    
    private void viewNotificationDetail(Notification notification) {
        // Increment view count
        notificationService.incrementViewCount(notification.getNotificationId());
        
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Chi tiết thông báo");
        alert.setHeaderText(notification.getTypeIcon() + " " + notification.getTitle());
        
        StringBuilder content = new StringBuilder();
        content.append("📋 Nội dung:\n").append(notification.getContent()).append("\n\n");
        content.append("🏷️ Loại: ").append(getTypeDisplayName(notification.getType())).append("\n");
        content.append("⚡ Mức độ: ").append(getPriorityDisplayName(notification.getPriority())).append("\n");
        content.append("👤 Tạo bởi: ").append(notification.getCreatedBy()).append("\n");
        content.append("📅 Ngày tạo: ").append(notification.getFormattedCreatedAt()).append("\n");
        content.append("⏰ Hết hạn: ").append(notification.getFormattedExpiryDate()).append("\n");
        
        if (notification.isExpired()) {
            content.append("\n⚠️ Thông báo này đã hết hạn.");
        }
        
        TextArea textArea = new TextArea(content.toString());
        textArea.setEditable(false);
        textArea.setWrapText(true);
        textArea.setPrefSize(500, 400);
        
        alert.getDialogPane().setContent(textArea);
        alert.showAndWait();
        
        loadNotifications(); // Refresh to update view count
    }
    
    private void markAsRead(Notification notification) {
        // For now, just show a confirmation
        showAlert("Thành công", "Đã đánh dấu thông báo \"" + notification.getTitle() + "\" là đã đọc!");
        // In future, implement read status tracking
    }
    
    private String getTypeDisplayName(Notification.NotificationType type) {
        return switch (type) {
            case GENERAL -> "Thông báo chung";
            case ACADEMIC -> "Học tập";
            case EXAM -> "Thi cử";
            case EVENT -> "Sự kiện";
            case URGENT -> "Khẩn cấp";
        };
    }
    
    private String getPriorityDisplayName(Notification.Priority priority) {
        return switch (priority) {
            case LOW -> "Thấp";
            case NORMAL -> "Bình thường";
            case HIGH -> "Cao";
            case URGENT -> "Khẩn cấp";
        };
    }
    
    @FXML
    private void refreshData() {
        loadNotifications();
        showAlert("Thành công", "Dữ liệu đã được làm mới!");
    }
    
    @FXML
    private void goBack() {
        try {
            if (currentUser.getRole() == User.UserRole.STUDENT) {
                SceneManager.switchScene("/com/example/doancuoikyjava/student-dashboard.fxml", 
                                       "Sinh viên - " + currentUser.getFullName());
            } else if (currentUser.getRole() == User.UserRole.TEACHER) {
                SceneManager.switchScene("/com/example/doancuoikyjava/teacher-dashboard.fxml", 
                                       "Giảng viên - " + currentUser.getFullName());
            }
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại trang chính: " + e.getMessage());
        }
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
