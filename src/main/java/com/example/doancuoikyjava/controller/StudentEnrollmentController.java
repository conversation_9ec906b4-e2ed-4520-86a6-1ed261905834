package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.service.*;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.HBox;

import java.io.IOException;
import java.net.URL;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

public class StudentEnrollmentController implements Initializable {
    
    @FXML private Label welcomeLabel;
    @FXML private Button backButton;
    @FXML private Button refreshBtn;
    @FXML private Button searchBtn;
    @FXML private Button exportBtn;
    
    // Student info labels
    @FXML private Label studentIdLabel;
    @FXML private Label studentNameLabel;
    @FXML private Label classNameLabel;
    @FXML private Label registeredCreditsLabel;
    @FXML private Label maxCreditsLabel;
    
    // Filters
    @FXML private ComboBox<String> departmentFilterComboBox;
    @FXML private ComboBox<Integer> creditsFilterComboBox;
    @FXML private ComboBox<String> timeFilterComboBox;
    @FXML private TextField searchField;
    
    // Available courses table
    @FXML private TableView<Course> availableCoursesTableView;
    @FXML private TableColumn<Course, String> courseIdColumn;
    @FXML private TableColumn<Course, String> courseNameColumn;
    @FXML private TableColumn<Course, Integer> creditsColumn;
    @FXML private TableColumn<Course, String> teacherColumn;
    @FXML private TableColumn<Course, String> dayColumn;
    @FXML private TableColumn<Course, String> scheduleColumn;
    @FXML private TableColumn<Course, String> classroomColumn;
    @FXML private TableColumn<Course, String> enrolledColumn;
    @FXML private TableColumn<Course, Integer> maxStudentsColumn;
    @FXML private TableColumn<Course, Void> actionsColumn;
    
    // Enrolled courses table
    @FXML private TableView<Course> enrolledCoursesTableView;
    @FXML private TableColumn<Course, String> enrolledCourseIdColumn;
    @FXML private TableColumn<Course, String> enrolledCourseNameColumn;
    @FXML private TableColumn<Course, Integer> enrolledCreditsColumn;
    @FXML private TableColumn<Course, String> enrolledTeacherColumn;
    @FXML private TableColumn<Course, String> enrolledDayColumn;
    @FXML private TableColumn<Course, String> enrolledScheduleColumn;
    @FXML private TableColumn<Course, String> enrolledClassroomColumn;
    @FXML private TableColumn<Course, String> enrollmentDateColumn;
    @FXML private TableColumn<Course, Void> enrolledActionsColumn;
    
    @FXML private Label totalCoursesLabel;
    @FXML private Label enrolledCoursesCountLabel;
    
    private CourseService courseService;
    private ScheduleService scheduleService;
    private UserService userService;
    private Student currentStudent;
    private ObservableList<Course> allCourses;
    private ObservableList<Course> filteredCourses;
    private ObservableList<Course> enrolledCourses;
    
    private static final int MAX_CREDITS = 24;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        courseService = new CourseService();
        scheduleService = new ScheduleService();
        userService = new UserService();
        allCourses = FXCollections.observableArrayList();
        filteredCourses = FXCollections.observableArrayList();
        enrolledCourses = FXCollections.observableArrayList();
        
        setupCurrentStudent();
        setupWelcomeMessage();
        setupStudentInfo();
        setupTableColumns();
        setupFilters();
        loadData();
    }
    
    private void setupCurrentStudent() {
        User currentUser = SceneManager.getCurrentUser();
        if (currentUser instanceof Student) {
            currentStudent = (Student) currentUser;
        }
    }
    
    private void setupWelcomeMessage() {
        if (currentStudent != null) {
            welcomeLabel.setText("Xin chào, " + currentStudent.getFullName());
        }
    }
    
    private void setupStudentInfo() {
        if (currentStudent != null) {
            studentIdLabel.setText(currentStudent.getStudentId());
            studentNameLabel.setText(currentStudent.getFullName());
            classNameLabel.setText(currentStudent.getClassName());
            maxCreditsLabel.setText(String.valueOf(MAX_CREDITS));
        }
    }
    
    private void setupTableColumns() {
        // Available courses table
        courseIdColumn.setCellValueFactory(new PropertyValueFactory<>("courseId"));
        courseNameColumn.setCellValueFactory(new PropertyValueFactory<>("courseName"));
        creditsColumn.setCellValueFactory(new PropertyValueFactory<>("credits"));
        teacherColumn.setCellValueFactory(new PropertyValueFactory<>("teacherName"));

        // Day and schedule columns with enhanced data
        dayColumn.setCellValueFactory(cellData -> {
            Course course = cellData.getValue();
            return new SimpleStringProperty(extractDayFromSchedule(course));
        });

        scheduleColumn.setCellValueFactory(cellData -> {
            Course course = cellData.getValue();
            return new SimpleStringProperty(extractTimeFromSchedule(course));
        });

        classroomColumn.setCellValueFactory(new PropertyValueFactory<>("classroom"));
        maxStudentsColumn.setCellValueFactory(new PropertyValueFactory<>("maxStudents"));
        
        // Enrolled count column
        enrolledColumn.setCellValueFactory(cellData -> {
            Course course = cellData.getValue();
            int enrolled = course.getEnrolledStudents().size();
            int max = course.getMaxStudents();
            return new SimpleStringProperty(enrolled + "/" + max);
        });
        
        // Actions column for available courses
        actionsColumn.setCellFactory(param -> new TableCell<Course, Void>() {
            private final Button enrollBtn = new Button("Đăng ký");
            
            {
                enrollBtn.setStyle("-fx-background-color: #4CAF50; -fx-text-fill: white; -fx-font-size: 10px;");
                
                enrollBtn.setOnAction(e -> {
                    Course course = getTableView().getItems().get(getIndex());
                    enrollInCourse(course);
                });
            }
            
            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    Course course = getTableView().getItems().get(getIndex());
                    boolean canEnroll = canEnrollInCourse(course);
                    enrollBtn.setDisable(!canEnroll);
                    setGraphic(enrollBtn);
                }
            }
        });
        
        // Enrolled courses table
        enrolledCourseIdColumn.setCellValueFactory(new PropertyValueFactory<>("courseId"));
        enrolledCourseNameColumn.setCellValueFactory(new PropertyValueFactory<>("courseName"));
        enrolledCreditsColumn.setCellValueFactory(new PropertyValueFactory<>("credits"));
        enrolledTeacherColumn.setCellValueFactory(new PropertyValueFactory<>("teacherName"));

        // Day and schedule columns for enrolled courses
        enrolledDayColumn.setCellValueFactory(cellData -> {
            Course course = cellData.getValue();
            return new SimpleStringProperty(extractDayFromSchedule(course));
        });

        enrolledScheduleColumn.setCellValueFactory(cellData -> {
            Course course = cellData.getValue();
            return new SimpleStringProperty(extractTimeFromSchedule(course));
        });

        enrolledClassroomColumn.setCellValueFactory(new PropertyValueFactory<>("classroom"));
        
        // Enrollment date column (placeholder)
        enrollmentDateColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(LocalDate.now().toString()));
        
        // Actions column for enrolled courses
        enrolledActionsColumn.setCellFactory(param -> new TableCell<Course, Void>() {
            private final Button unenrollBtn = new Button("Hủy đăng ký");
            
            {
                unenrollBtn.setStyle("-fx-background-color: #F44336; -fx-text-fill: white; -fx-font-size: 10px;");
                
                unenrollBtn.setOnAction(e -> {
                    Course course = getTableView().getItems().get(getIndex());
                    unenrollFromCourse(course);
                });
            }
            
            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(unenrollBtn);
                }
            }
        });
        
        availableCoursesTableView.setItems(filteredCourses);
        enrolledCoursesTableView.setItems(enrolledCourses);
    }

    /**
     * Extract day information from course schedule
     */
    private String extractDayFromSchedule(Course course) {
        try {
            // Try to get detailed schedule information first
            List<CourseSchedule> schedules = scheduleService.getSchedulesByCourse(course.getCourseId());
            if (!schedules.isEmpty()) {
                // Get the first schedule's day
                return schedules.get(0).getDayOfWeekText();
            }

            // Fallback to parsing from course.getSchedule() string
            String schedule = course.getSchedule();
            if (schedule != null && !schedule.trim().isEmpty()) {
                // Parse format like "Thứ 2, 4, 6 - 7:30-9:30"
                if (schedule.contains("Thứ")) {
                    String[] parts = schedule.split("-");
                    if (parts.length > 0) {
                        String dayPart = parts[0].trim();
                        // Extract first day mentioned
                        if (dayPart.contains("Thứ 2")) return "Thứ 2";
                        if (dayPart.contains("Thứ 3")) return "Thứ 3";
                        if (dayPart.contains("Thứ 4")) return "Thứ 4";
                        if (dayPart.contains("Thứ 5")) return "Thứ 5";
                        if (dayPart.contains("Thứ 6")) return "Thứ 6";
                        if (dayPart.contains("Thứ 7")) return "Thứ 7";
                        if (dayPart.contains("Chủ nhật")) return "Chủ nhật";
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("❌ Error extracting day from schedule: " + e.getMessage());
        }

        return "Chưa xếp";
    }

    /**
     * Extract time information from course schedule
     */
    private String extractTimeFromSchedule(Course course) {
        try {
            // Try to get detailed schedule information first
            List<CourseSchedule> schedules = scheduleService.getSchedulesByCourse(course.getCourseId());
            if (!schedules.isEmpty()) {
                // Get the first schedule's time range
                return schedules.get(0).getTimeRange();
            }

            // Fallback to parsing from course.getSchedule() string
            String schedule = course.getSchedule();
            if (schedule != null && !schedule.trim().isEmpty()) {
                // Parse format like "Thứ 2, 4, 6 - 7:30-9:30"
                if (schedule.contains("-")) {
                    String[] parts = schedule.split("-");
                    if (parts.length >= 2) {
                        // Look for time pattern like "7:30-9:30"
                        String timePart = parts[parts.length - 1].trim();
                        if (timePart.matches(".*\\d{1,2}:\\d{2}.*")) {
                            return timePart;
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("❌ Error extracting time from schedule: " + e.getMessage());
        }

        return "Chưa xếp";
    }
    
    private void setupFilters() {
        // Setup credits filter
        creditsFilterComboBox.setItems(FXCollections.observableArrayList(1, 2, 3, 4, 5));
        
        // Setup time filter
        timeFilterComboBox.setItems(FXCollections.observableArrayList(
            "Sáng (7:30-11:30)", "Chiều (13:30-17:30)", "Tối (18:00-21:00)"
        ));
        
        // Setup search functionality
        searchField.textProperty().addListener((obs, oldText, newText) -> filterCourses());
        departmentFilterComboBox.valueProperty().addListener((obs, oldValue, newValue) -> filterCourses());
        creditsFilterComboBox.valueProperty().addListener((obs, oldValue, newValue) -> filterCourses());
        timeFilterComboBox.valueProperty().addListener((obs, oldValue, newValue) -> filterCourses());
    }
    
    private void loadData() {
        // Load all courses
        allCourses.setAll(courseService.getAllCourses());
        
        // Load enrolled courses for current student
        loadEnrolledCourses();
        
        // Update filters
        updateFilters();
        filterCourses();
        updateStatistics();
    }
    
    private void loadEnrolledCourses() {
        if (currentStudent == null) return;
        
        List<Course> studentCourses = allCourses.stream()
                .filter(course -> course.getEnrolledStudents().contains(currentStudent.getStudentId()))
                .collect(Collectors.toList());
        
        enrolledCourses.setAll(studentCourses);
        
        // Update registered credits
        int totalCredits = studentCourses.stream()
                .mapToInt(Course::getCredits)
                .sum();
        registeredCreditsLabel.setText(String.valueOf(totalCredits));
    }
    
    private void updateFilters() {
        // Update department filter (using teacher names as departments for now)
        List<String> departments = allCourses.stream()
                .map(Course::getTeacherName)
                .filter(name -> name != null && !name.isEmpty())
                .distinct()
                .sorted()
                .collect(Collectors.toList());
        departmentFilterComboBox.setItems(FXCollections.observableArrayList(departments));
    }
    
    private void filterCourses() {
        String searchText = searchField.getText().toLowerCase();
        String selectedDepartment = departmentFilterComboBox.getValue();
        Integer selectedCredits = creditsFilterComboBox.getValue();
        String selectedTime = timeFilterComboBox.getValue();
        
        List<Course> filtered = allCourses.stream()
                .filter(course -> {
                    // Don't show courses already enrolled
                    if (currentStudent != null && 
                        course.getEnrolledStudents().contains(currentStudent.getStudentId())) {
                        return false;
                    }
                    
                    boolean matchesSearch = searchText.isEmpty() || 
                            course.getCourseName().toLowerCase().contains(searchText) ||
                            course.getCourseId().toLowerCase().contains(searchText);
                    
                    boolean matchesDepartment = selectedDepartment == null || 
                            (course.getTeacherName() != null && course.getTeacherName().equals(selectedDepartment));
                    
                    boolean matchesCredits = selectedCredits == null || 
                            course.getCredits() == selectedCredits;
                    
                    boolean matchesTime = selectedTime == null || 
                            (course.getSchedule() != null && course.getSchedule().contains("7:30") && selectedTime.contains("Sáng")) ||
                            (course.getSchedule() != null && course.getSchedule().contains("13:30") && selectedTime.contains("Chiều"));
                    
                    return matchesSearch && matchesDepartment && matchesCredits && matchesTime;
                })
                .collect(Collectors.toList());
        
        filteredCourses.setAll(filtered);
        updateStatistics();
    }
    
    private void updateStatistics() {
        totalCoursesLabel.setText("Tổng môn học có thể đăng ký: " + filteredCourses.size());
        enrolledCoursesCountLabel.setText("Đã đăng ký: " + enrolledCourses.size() + " môn");
    }
    
    private boolean canEnrollInCourse(Course course) {
        if (currentStudent == null) return false;
        
        // Check if already enrolled
        if (course.getEnrolledStudents().contains(currentStudent.getStudentId())) {
            return false;
        }
        
        // Check if course is full
        if (course.getEnrolledStudents().size() >= course.getMaxStudents()) {
            return false;
        }
        
        // Check credit limit
        int currentCredits = Integer.parseInt(registeredCreditsLabel.getText());
        if (currentCredits + course.getCredits() > MAX_CREDITS) {
            return false;
        }
        
        return true;
    }
    
    private void enrollInCourse(Course course) {
        if (!canEnrollInCourse(course)) {
            showAlert("Không thể đăng ký", "Không thể đăng ký môn học này. Vui lòng kiểm tra điều kiện đăng ký.");
            return;
        }
        
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Xác nhận đăng ký");
        alert.setHeaderText("Đăng ký môn học: " + course.getCourseName());
        alert.setContentText("Bạn có chắc chắn muốn đăng ký môn học này?\n\n" +
                           "Mã môn học: " + course.getCourseId() + "\n" +
                           "Tín chỉ: " + course.getCredits() + "\n" +
                           "Giáo viên: " + course.getTeacherName() + "\n" +
                           "Lịch học: " + course.getSchedule());
        
        Optional<ButtonType> result = alert.showAndWait();
        if (result.get() == ButtonType.OK) {
            if (courseService.enrollStudent(course.getCourseId(), currentStudent.getStudentId())) {
                showAlert("Thành công", "Đăng ký môn học thành công!");
                loadData(); // Refresh data
            } else {
                showAlert("Lỗi", "Không thể đăng ký môn học. Vui lòng thử lại.");
            }
        }
    }
    
    private void unenrollFromCourse(Course course) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Xác nhận hủy đăng ký");
        alert.setHeaderText("Hủy đăng ký môn học: " + course.getCourseName());
        alert.setContentText("Bạn có chắc chắn muốn hủy đăng ký môn học này?");
        
        Optional<ButtonType> result = alert.showAndWait();
        if (result.get() == ButtonType.OK) {
            if (courseService.unenrollStudent(course.getCourseId(), currentStudent.getStudentId())) {
                showAlert("Thành công", "Hủy đăng ký môn học thành công!");
                loadData(); // Refresh data
            } else {
                showAlert("Lỗi", "Không thể hủy đăng ký môn học. Vui lòng thử lại.");
            }
        }
    }
    
    @FXML
    private void goBack() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/student-dashboard.fxml", 
                                   "Sinh viên - " + SceneManager.getCurrentUser().getFullName());
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại trang chính: " + e.getMessage());
        }
    }
    
    @FXML
    private void refreshData() {
        loadData();
        showAlert("Thành công", "Dữ liệu đã được làm mới!");
    }
    
    @FXML
    private void searchCourses() {
        filterCourses();
    }
    
    @FXML
    private void exportEnrollment() {
        StringBuilder report = new StringBuilder();
        report.append("DANH SÁCH ĐĂNG KÝ MÔN HỌC\n");
        report.append("===========================\n\n");
        
        if (currentStudent != null) {
            report.append("Sinh viên: ").append(currentStudent.getFullName()).append("\n");
            report.append("MSSV: ").append(currentStudent.getStudentId()).append("\n");
            report.append("Lớp: ").append(currentStudent.getClassName()).append("\n");
            report.append("Tín chỉ đã đăng ký: ").append(registeredCreditsLabel.getText()).append("/").append(MAX_CREDITS).append("\n\n");
        }
        
        report.append("MÔN HỌC ĐÃ ĐĂNG KÝ:\n");
        report.append("Mã MH\tTên môn học\t\tTín chỉ\tGiáo viên\tLịch học\n");
        report.append("----------------------------------------------------------------\n");
        
        for (Course course : enrolledCourses) {
            report.append(course.getCourseId()).append("\t");
            report.append(course.getCourseName()).append("\t");
            report.append(course.getCredits()).append("\t");
            report.append(course.getTeacherName()).append("\t");
            report.append(course.getSchedule()).append("\n");
        }
        
        // Show report in a dialog
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Danh sách đăng ký");
        alert.setHeaderText("Danh sách môn học đã đăng ký");
        
        TextArea textArea = new TextArea(report.toString());
        textArea.setEditable(false);
        textArea.setWrapText(true);
        textArea.setPrefSize(600, 400);
        
        alert.getDialogPane().setContent(textArea);
        alert.showAndWait();
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
