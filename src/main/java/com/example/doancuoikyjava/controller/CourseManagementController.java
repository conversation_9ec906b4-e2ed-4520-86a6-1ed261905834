package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.Course;
import com.example.doancuoikyjava.model.CourseSchedule;
import com.example.doancuoikyjava.model.Teacher;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.CourseService;
import com.example.doancuoikyjava.service.ScheduleService;
import com.example.doancuoikyjava.service.UserService;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;

import java.io.IOException;
import java.net.URL;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

public class CourseManagementController implements Initializable {
    
    @FXML private Label welcomeLabel;
    @FXML private Button backButton;
    @FXML private Button addCourseBtn;
    @FXML private Button refreshBtn;
    @FXML private Button searchBtn;
    @FXML private Button exportBtn;
    @FXML private Button deleteSelectedBtn;
    
    @FXML private TextField searchField;
    @FXML private ComboBox<String> teacherFilterComboBox;
    @FXML private ComboBox<Integer> creditsFilterComboBox;
    
    @FXML private TableView<Course> coursesTableView;
    @FXML private TableColumn<Course, String> courseIdColumn;
    @FXML private TableColumn<Course, String> courseNameColumn;
    @FXML private TableColumn<Course, Integer> creditsColumn;
    @FXML private TableColumn<Course, String> teacherNameColumn;
    @FXML private TableColumn<Course, String> scheduleColumn;
    @FXML private TableColumn<Course, String> classroomColumn;
    @FXML private TableColumn<Course, String> enrolledColumn;
    @FXML private TableColumn<Course, Integer> maxStudentsColumn;
    @FXML private TableColumn<Course, Void> actionsColumn;
    
    @FXML private Label totalCoursesLabel;
    
    private CourseService courseService;
    private ScheduleService scheduleService;
    private UserService userService;
    private ObservableList<Course> allCourses;
    private ObservableList<Course> filteredCourses;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        courseService = new CourseService();
        scheduleService = new ScheduleService();
        userService = new UserService();
        allCourses = FXCollections.observableArrayList();
        filteredCourses = FXCollections.observableArrayList();
        
        setupWelcomeMessage();
        setupTableColumns();
        setupFilters();
        loadCourses();
        
        // Enable multiple selection
        coursesTableView.getSelectionModel().setSelectionMode(SelectionMode.MULTIPLE);
    }
    
    private void setupWelcomeMessage() {
        User currentUser = SceneManager.getCurrentUser();
        if (currentUser != null) {
            welcomeLabel.setText("Xin chào, " + currentUser.getFullName());
        }
    }
    
    private void setupTableColumns() {
        courseIdColumn.setCellValueFactory(new PropertyValueFactory<>("courseId"));
        courseNameColumn.setCellValueFactory(new PropertyValueFactory<>("courseName"));
        creditsColumn.setCellValueFactory(new PropertyValueFactory<>("credits"));
        teacherNameColumn.setCellValueFactory(new PropertyValueFactory<>("teacherName"));

        // Schedule column - show only time
        scheduleColumn.setCellValueFactory(cellData -> {
            Course course = cellData.getValue();
            return new SimpleStringProperty(extractTimeFromSchedule(course));
        });

        // Classroom column - show actual classroom or get from schedule
        classroomColumn.setCellValueFactory(cellData -> {
            Course course = cellData.getValue();
            return new SimpleStringProperty(getActualClassroom(course));
        });

        maxStudentsColumn.setCellValueFactory(new PropertyValueFactory<>("maxStudents"));
        
        // Enrolled students column with formatting
        enrolledColumn.setCellValueFactory(cellData -> {
            Course course = cellData.getValue();
            int enrolled = course.getEnrolledStudents().size();
            int max = course.getMaxStudents();
            return new SimpleStringProperty(enrolled + "/" + max);
        });
        
        // Actions column with buttons
        actionsColumn.setCellFactory(param -> new TableCell<Course, Void>() {
            private final Button editBtn = new Button("Sửa");
            private final Button deleteBtn = new Button("Xóa");
            private final Button studentsBtn = new Button("SV");
            private final HBox buttons = new HBox(3, editBtn, deleteBtn, studentsBtn);
            
            {
                editBtn.setStyle("-fx-background-color: #2196F3; -fx-text-fill: white; -fx-font-size: 9px;");
                deleteBtn.setStyle("-fx-background-color: #F44336; -fx-text-fill: white; -fx-font-size: 9px;");
                studentsBtn.setStyle("-fx-background-color: #4CAF50; -fx-text-fill: white; -fx-font-size: 9px;");
                
                editBtn.setOnAction(e -> {
                    Course course = getTableView().getItems().get(getIndex());
                    showEditCourseDialog(course);
                });
                
                deleteBtn.setOnAction(e -> {
                    Course course = getTableView().getItems().get(getIndex());
                    deleteCourse(course);
                });
                
                studentsBtn.setOnAction(e -> {
                    Course course = getTableView().getItems().get(getIndex());
                    showCourseStudents(course);
                });
            }
            
            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(buttons);
                }
            }
        });
        
        coursesTableView.setItems(filteredCourses);
    }
    
    private void setupFilters() {
        // Setup search functionality
        searchField.textProperty().addListener((obs, oldText, newText) -> filterCourses());
        teacherFilterComboBox.valueProperty().addListener((obs, oldValue, newValue) -> filterCourses());
        creditsFilterComboBox.valueProperty().addListener((obs, oldValue, newValue) -> filterCourses());
        
        // Setup credits filter
        creditsFilterComboBox.setItems(FXCollections.observableArrayList(1, 2, 3, 4, 5));
    }
    
    private void loadCourses() {
        allCourses.setAll(courseService.getAllCourses());
        updateFilters();
        filterCourses();
        updateTotalLabel();
    }
    
    private void updateFilters() {
        // Update teacher filter
        List<String> teachers = allCourses.stream()
                .map(Course::getTeacherName)
                .filter(name -> name != null && !name.isEmpty())
                .distinct()
                .sorted()
                .collect(Collectors.toList());
        teacherFilterComboBox.setItems(FXCollections.observableArrayList(teachers));
    }
    
    private void filterCourses() {
        String searchText = searchField.getText().toLowerCase();
        String selectedTeacher = teacherFilterComboBox.getValue();
        Integer selectedCredits = creditsFilterComboBox.getValue();
        
        List<Course> filtered = allCourses.stream()
                .filter(course -> {
                    boolean matchesSearch = searchText.isEmpty() || 
                            course.getCourseName().toLowerCase().contains(searchText) ||
                            course.getCourseId().toLowerCase().contains(searchText) ||
                            (course.getDescription() != null && course.getDescription().toLowerCase().contains(searchText));
                    
                    boolean matchesTeacher = selectedTeacher == null || 
                            (course.getTeacherName() != null && course.getTeacherName().equals(selectedTeacher));
                    
                    boolean matchesCredits = selectedCredits == null || 
                            course.getCredits() == selectedCredits;
                    
                    return matchesSearch && matchesTeacher && matchesCredits;
                })
                .collect(Collectors.toList());
        
        filteredCourses.setAll(filtered);
        updateTotalLabel();
    }
    
    private void updateTotalLabel() {
        totalCoursesLabel.setText("Tổng số môn học: " + filteredCourses.size() + "/" + allCourses.size());
    }
    
    @FXML
    private void goBack() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/admin-dashboard.fxml", 
                                   "Quản trị viên - " + SceneManager.getCurrentUser().getFullName());
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại trang chính: " + e.getMessage());
        }
    }
    
    @FXML
    private void showAddCourseDialog() {
        Dialog<Course> dialog = createCourseDialog(null);
        Optional<Course> result = dialog.showAndWait();
        
        result.ifPresent(course -> {
            if (courseService.addCourse(course)) {
                showAlert("Thành công", "Thêm môn học thành công!");
                loadCourses();
            } else {
                showAlert("Lỗi", "Không thể thêm môn học. Mã môn học có thể đã tồn tại.");
            }
        });
    }
    
    private void showEditCourseDialog(Course course) {
        Dialog<Course> dialog = createCourseDialog(course);
        Optional<Course> result = dialog.showAndWait();
        
        result.ifPresent(updatedCourse -> {
            if (courseService.updateCourse(updatedCourse)) {
                showAlert("Thành công", "Cập nhật môn học thành công!");
                loadCourses();
            } else {
                showAlert("Lỗi", "Không thể cập nhật môn học.");
            }
        });
    }
    
    private Dialog<Course> createCourseDialog(Course existingCourse) {
        Dialog<Course> dialog = new Dialog<>();
        dialog.setTitle(existingCourse == null ? "Thêm môn học mới" : "Sửa thông tin môn học");
        dialog.setHeaderText(null);
        
        ButtonType saveButtonType = new ButtonType("Lưu", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(saveButtonType, ButtonType.CANCEL);
        
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));
        
        TextField courseIdField = new TextField();
        TextField courseNameField = new TextField();
        TextArea descriptionArea = new TextArea();
        ComboBox<Integer> creditsComboBox = new ComboBox<>();
        ComboBox<String> teacherComboBox = new ComboBox<>();
        TextField scheduleField = new TextField();
        TextField classroomField = new TextField();
        TextField maxStudentsField = new TextField();
        
        creditsComboBox.setItems(FXCollections.observableArrayList(1, 2, 3, 4, 5));
        descriptionArea.setPrefRowCount(3);
        
        // Load teachers
        List<User> teachers = userService.getUsersByRole(User.UserRole.TEACHER);
        List<String> teacherNames = teachers.stream()
                .map(User::getFullName)
                .collect(Collectors.toList());
        teacherComboBox.setItems(FXCollections.observableArrayList(teacherNames));
        
        if (existingCourse != null) {
            courseIdField.setText(existingCourse.getCourseId());
            courseIdField.setDisable(true); // Don't allow editing course ID
            courseNameField.setText(existingCourse.getCourseName());
            descriptionArea.setText(existingCourse.getDescription());
            creditsComboBox.setValue(existingCourse.getCredits());
            teacherComboBox.setValue(existingCourse.getTeacherName());
            scheduleField.setText(existingCourse.getSchedule());
            classroomField.setText(existingCourse.getClassroom());
            maxStudentsField.setText(String.valueOf(existingCourse.getMaxStudents()));
        } else {
            courseIdField.setText(courseService.generateNextCourseId());
        }
        
        grid.add(new Label("Mã môn học:"), 0, 0);
        grid.add(courseIdField, 1, 0);
        grid.add(new Label("Tên môn học:"), 0, 1);
        grid.add(courseNameField, 1, 1);
        grid.add(new Label("Mô tả:"), 0, 2);
        grid.add(descriptionArea, 1, 2);
        grid.add(new Label("Số tín chỉ:"), 0, 3);
        grid.add(creditsComboBox, 1, 3);
        grid.add(new Label("Giáo viên:"), 0, 4);
        grid.add(teacherComboBox, 1, 4);
        grid.add(new Label("Lịch học:"), 0, 5);
        grid.add(scheduleField, 1, 5);
        grid.add(new Label("Phòng học:"), 0, 6);
        grid.add(classroomField, 1, 6);
        grid.add(new Label("Sĩ số tối đa:"), 0, 7);
        grid.add(maxStudentsField, 1, 7);
        
        dialog.getDialogPane().setContent(grid);
        
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == saveButtonType) {
                try {
                    Course course = existingCourse != null ? existingCourse : new Course();
                    
                    course.setCourseId(courseIdField.getText());
                    course.setCourseName(courseNameField.getText());
                    course.setDescription(descriptionArea.getText());
                    course.setCredits(creditsComboBox.getValue());
                    course.setTeacherName(teacherComboBox.getValue());
                    course.setSchedule(scheduleField.getText());
                    course.setClassroom(classroomField.getText());
                    course.setMaxStudents(Integer.parseInt(maxStudentsField.getText()));
                    
                    // Find teacher ID by name
                    String teacherName = teacherComboBox.getValue();
                    if (teacherName != null) {
                        Teacher teacher = (Teacher) teachers.stream()
                                .filter(t -> t.getFullName().equals(teacherName))
                                .findFirst()
                                .orElse(null);
                        if (teacher != null) {
                            course.setTeacherId(teacher.getTeacherId());
                        }
                    }
                    
                    return course;
                } catch (Exception e) {
                    showAlert("Lỗi", "Dữ liệu không hợp lệ: " + e.getMessage());
                    return null;
                }
            }
            return null;
        });
        
        return dialog;
    }
    
    private void deleteCourse(Course course) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Xác nhận xóa");
        alert.setHeaderText("Bạn có chắc chắn muốn xóa môn học này?");
        alert.setContentText("Môn học: " + course.getCourseName() + " (" + course.getCourseId() + ")");
        
        Optional<ButtonType> result = alert.showAndWait();
        if (result.get() == ButtonType.OK) {
            if (courseService.deleteCourse(course.getCourseId())) {
                showAlert("Thành công", "Xóa môn học thành công!");
                loadCourses();
            } else {
                showAlert("Lỗi", "Không thể xóa môn học.");
            }
        }
    }
    
    private void showCourseStudents(Course course) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Danh sách sinh viên");
        alert.setHeaderText("Môn học: " + course.getCourseName());
        
        if (course.getEnrolledStudents().isEmpty()) {
            alert.setContentText("Chưa có sinh viên nào đăng ký môn học này.");
        } else {
            StringBuilder content = new StringBuilder();
            content.append("Số sinh viên đã đăng ký: ").append(course.getEnrolledStudents().size())
                   .append("/").append(course.getMaxStudents()).append("\n\n");
            
            for (String studentId : course.getEnrolledStudents()) {
                User student = userService.getUserById(studentId);
                if (student != null) {
                    content.append("- ").append(student.getFullName())
                           .append(" (").append(studentId).append(")\n");
                }
            }
            alert.setContentText(content.toString());
        }
        
        alert.showAndWait();
    }
    
    @FXML
    private void deleteSelectedCourses() {
        List<Course> selectedCourses = coursesTableView.getSelectionModel().getSelectedItems();
        
        if (selectedCourses.isEmpty()) {
            showAlert("Thông báo", "Vui lòng chọn môn học cần xóa.");
            return;
        }
        
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Xác nhận xóa");
        alert.setHeaderText("Bạn có chắc chắn muốn xóa " + selectedCourses.size() + " môn học đã chọn?");
        
        Optional<ButtonType> result = alert.showAndWait();
        if (result.get() == ButtonType.OK) {
            int deletedCount = 0;
            for (Course course : selectedCourses) {
                if (courseService.deleteCourse(course.getCourseId())) {
                    deletedCount++;
                }
            }
            
            showAlert("Thành công", "Đã xóa " + deletedCount + "/" + selectedCourses.size() + " môn học.");
            loadCourses();
        }
    }
    
    @FXML
    private void refreshData() {
        loadCourses();
        showAlert("Thành công", "Dữ liệu đã được làm mới!");
    }
    
    @FXML
    private void searchCourses() {
        filterCourses();
    }
    

    
    /**
     * Extract time information from course schedule
     */
    private String extractTimeFromSchedule(Course course) {
        try {
            // Try to get detailed schedule information first
            List<CourseSchedule> schedules = scheduleService.getSchedulesByCourse(course.getCourseId());
            if (!schedules.isEmpty()) {
                // Get the first schedule's time range
                return schedules.get(0).getTimeRange();
            }

            // Fallback to parsing from course.getSchedule() string
            String schedule = course.getSchedule();
            if (schedule != null && !schedule.trim().isEmpty()) {
                // Parse format like "Thứ 2, 4, 6 - 7:30-9:30"
                if (schedule.contains("-")) {
                    String[] parts = schedule.split("-");
                    if (parts.length >= 2) {
                        // Look for time pattern like "7:30-9:30"
                        String timePart = parts[parts.length - 1].trim();
                        if (timePart.matches(".*\\d{1,2}:\\d{2}.*")) {
                            return timePart;
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("❌ Error extracting time from schedule: " + e.getMessage());
        }

        return "Chưa xếp lịch";
    }

    /**
     * Get actual classroom information
     */
    private String getActualClassroom(Course course) {
        try {
            // Try to get detailed schedule information first
            List<CourseSchedule> schedules = scheduleService.getSchedulesByCourse(course.getCourseId());
            if (!schedules.isEmpty()) {
                String classroom = schedules.get(0).getClassroom();
                if (classroom != null && !classroom.trim().isEmpty()) {
                    return classroom;
                }
            }

            // Fallback to course classroom
            String classroom = course.getClassroom();
            if (classroom != null && !classroom.trim().isEmpty()) {
                return classroom;
            }

        } catch (Exception e) {
            System.err.println("❌ Error getting classroom info: " + e.getMessage());
        }

        return "Chưa xếp phòng";
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
