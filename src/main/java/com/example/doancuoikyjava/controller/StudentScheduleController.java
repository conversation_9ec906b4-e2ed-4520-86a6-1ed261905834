package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.service.*;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;

import java.io.IOException;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

public class StudentScheduleController implements Initializable {
    
    @FXML private Label welcomeLabel;
    @FXML private Button backButton;
    @FXML private Button refreshBtn;
    @FXML private Button exportBtn;
    
    @FXML private Label totalCoursesLabel;
    @FXML private Label totalCreditsLabel;
    @FXML private Label currentSemesterLabel;
    
    @FXML private ComboBox<String> dayFilterComboBox;
    @FXML private ComboBox<String> timeFilterComboBox;
    @FXML private TextField searchField;
    
    @FXML private TableView<CourseSchedule> scheduleTableView;
    @FXML private TableColumn<CourseSchedule, String> courseIdColumn;
    @FXML private TableColumn<CourseSchedule, String> courseNameColumn;
    @FXML private TableColumn<CourseSchedule, Integer> creditsColumn;
    @FXML private TableColumn<CourseSchedule, String> dayColumn;
    @FXML private TableColumn<CourseSchedule, String> scheduleColumn;
    @FXML private TableColumn<CourseSchedule, String> classroomColumn;
    @FXML private TableColumn<CourseSchedule, String> teacherColumn;
    @FXML private TableColumn<CourseSchedule, Void> actionsColumn;
    
    private CourseService courseService;
    private ScheduleService scheduleService;
    private UserService userService;
    private Student currentStudent;
    private ObservableList<CourseSchedule> allSchedules;
    private ObservableList<CourseSchedule> filteredSchedules;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        courseService = new CourseService();
        scheduleService = new ScheduleService();
        userService = new UserService();
        allSchedules = FXCollections.observableArrayList();
        filteredSchedules = FXCollections.observableArrayList();

        setupCurrentStudent();
        setupWelcomeMessage();
        setupTableColumns();
        setupFilters();
        loadSchedule();
    }
    
    private void setupCurrentStudent() {
        User currentUser = SceneManager.getCurrentUser();
        if (currentUser instanceof Student) {
            currentStudent = (Student) currentUser;
        }
    }
    
    private void setupWelcomeMessage() {
        if (currentStudent != null) {
            welcomeLabel.setText("Lịch học - " + currentStudent.getFullName());
        }
    }
    
    private void setupTableColumns() {
        courseIdColumn.setCellValueFactory(cellData ->
            new SimpleStringProperty(cellData.getValue().getCourseId()));

        courseNameColumn.setCellValueFactory(cellData ->
            new SimpleStringProperty(cellData.getValue().getCourseName()));

        creditsColumn.setCellValueFactory(cellData -> {
            // Get credits from course
            String courseId = cellData.getValue().getCourseId();
            Course course = courseService.getCourseById(courseId);
            return new SimpleObjectProperty<>(course != null ? course.getCredits() : 0);
        });

        dayColumn.setCellValueFactory(cellData ->
            new SimpleStringProperty(cellData.getValue().getDayOfWeekText()));

        scheduleColumn.setCellValueFactory(cellData ->
            new SimpleStringProperty(cellData.getValue().getTimeRange()));

        classroomColumn.setCellValueFactory(cellData ->
            new SimpleStringProperty(cellData.getValue().getClassroom()));

        teacherColumn.setCellValueFactory(cellData -> {
            // Get teacher name from course
            String courseId = cellData.getValue().getCourseId();
            Course course = courseService.getCourseById(courseId);
            return new SimpleStringProperty(course != null ? course.getTeacherName() : "N/A");
        });

        // Actions column
        actionsColumn.setCellFactory(param -> new TableCell<CourseSchedule, Void>() {
            private final Button detailsBtn = new Button("Chi tiết");

            {
                detailsBtn.setStyle("-fx-background-color: #2196F3; -fx-text-fill: white; -fx-font-size: 10px;");
                detailsBtn.setOnAction(e -> {
                    CourseSchedule schedule = getTableView().getItems().get(getIndex());
                    showScheduleDetails(schedule);
                });
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(detailsBtn);
                }
            }
        });

        scheduleTableView.setItems(filteredSchedules);
    }
    
    private void setupFilters() {
        // Day filter
        dayFilterComboBox.getItems().addAll(
            "Tất cả ngày", "Thứ 2", "Thứ 3", "Thứ 4", "Thứ 5", "Thứ 6", "Thứ 7", "Chủ nhật"
        );
        dayFilterComboBox.setValue("Tất cả ngày");
        
        // Time filter
        timeFilterComboBox.getItems().addAll(
            "Tất cả thời gian", "Sáng (7:00-11:30)", "Chiều (13:00-17:30)", "Tối (18:00-21:30)"
        );
        timeFilterComboBox.setValue("Tất cả thời gian");
        
        // Search functionality
        searchField.textProperty().addListener((observable, oldValue, newValue) -> filterSchedule());
        dayFilterComboBox.setOnAction(e -> filterSchedule());
        timeFilterComboBox.setOnAction(e -> filterSchedule());
    }
    
    private void loadSchedule() {
        if (currentStudent == null) return;

        System.out.println("📅 Loading schedule for student: " + currentStudent.getStudentId());

        // Get schedules for student using ScheduleService
        List<CourseSchedule> studentSchedules = scheduleService.getSchedulesByStudent(currentStudent.getStudentId());

        // Enhance schedule data with course information
        for (CourseSchedule schedule : studentSchedules) {
            Course course = courseService.getCourseById(schedule.getCourseId());
            if (course != null) {
                schedule.setCourseName(course.getCourseName());
                // Set teacher info if not already set
                if (schedule.getTeacherId() == null || schedule.getTeacherId().isEmpty()) {
                    schedule.setTeacherId(course.getTeacherId());
                    schedule.setTeacherName(course.getTeacherName());
                }
            }
        }

        // FALLBACK: If no detailed schedules found, create from enrolled courses
        if (studentSchedules.isEmpty()) {
            System.out.println("⚠️ No detailed schedules found, creating from enrolled courses...");
            studentSchedules = createSchedulesFromEnrolledCourses();
        }

        allSchedules.setAll(studentSchedules);

        System.out.println("📊 Loaded " + studentSchedules.size() + " schedule entries for student");

        filterSchedule();
        updateStatistics();
    }

    /**
     * Create CourseSchedule objects from enrolled courses when detailed schedules are not available
     */
    private List<CourseSchedule> createSchedulesFromEnrolledCourses() {
        List<CourseSchedule> schedules = new ArrayList<>();

        // Get all courses
        List<Course> allCourses = courseService.getAllCourses();

        // Filter courses that student is enrolled in
        List<Course> enrolledCourses = allCourses.stream()
                .filter(course -> course.getEnrolledStudents() != null &&
                                course.getEnrolledStudents().contains(currentStudent.getStudentId()))
                .collect(Collectors.toList());

        System.out.println("📚 Found " + enrolledCourses.size() + " enrolled courses for student");

        for (Course course : enrolledCourses) {
            CourseSchedule schedule = createScheduleFromCourse(course);
            if (schedule != null) {
                schedules.add(schedule);
                System.out.println("✅ Created schedule for: " + course.getCourseName());
            }
        }

        return schedules;
    }

    /**
     * Create a CourseSchedule object from a Course object
     */
    private CourseSchedule createScheduleFromCourse(Course course) {
        try {
            CourseSchedule schedule = new CourseSchedule();

            schedule.setCourseId(course.getCourseId());
            schedule.setCourseName(course.getCourseName());
            schedule.setTeacherId(course.getTeacherId());
            schedule.setTeacherName(course.getTeacherName());

            // Parse schedule string to extract day and time
            String scheduleStr = course.getSchedule();
            if (scheduleStr != null && !scheduleStr.trim().isEmpty()) {
                parseScheduleString(schedule, scheduleStr);
            } else {
                // Default values if no schedule info
                schedule.setDayOfWeek(2); // Monday
                schedule.setStartTime(LocalTime.of(7, 30));
                schedule.setEndTime(LocalTime.of(9, 30));
            }

            // Set classroom
            String classroom = course.getClassroom();
            if (classroom != null && !classroom.trim().isEmpty()) {
                schedule.setClassroom(classroom);
            } else {
                schedule.setClassroom("TBA");
            }

            // Set default values
            schedule.setWeekType("ALL");
            schedule.setSemester("Học kỳ 1");
            schedule.setAcademicYear("2024");
            schedule.setActive(true);

            return schedule;

        } catch (Exception e) {
            System.err.println("❌ Error creating schedule from course " + course.getCourseId() + ": " + e.getMessage());
            return null;
        }
    }

    /**
     * Parse schedule string like "Thứ 2, 4, 6 - 7:30-9:30" to extract day and time
     */
    private void parseScheduleString(CourseSchedule schedule, String scheduleStr) {
        try {
            // Default values
            schedule.setDayOfWeek(2); // Monday
            schedule.setStartTime(LocalTime.of(7, 30));
            schedule.setEndTime(LocalTime.of(9, 30));

            if (scheduleStr.contains("-")) {
                String[] parts = scheduleStr.split("-");

                // Parse day part
                if (parts.length > 0) {
                    String dayPart = parts[0].trim().toLowerCase();
                    if (dayPart.contains("thứ 2")) schedule.setDayOfWeek(2);
                    else if (dayPart.contains("thứ 3")) schedule.setDayOfWeek(3);
                    else if (dayPart.contains("thứ 4")) schedule.setDayOfWeek(4);
                    else if (dayPart.contains("thứ 5")) schedule.setDayOfWeek(5);
                    else if (dayPart.contains("thứ 6")) schedule.setDayOfWeek(6);
                    else if (dayPart.contains("thứ 7")) schedule.setDayOfWeek(7);
                    else if (dayPart.contains("chủ nhật")) schedule.setDayOfWeek(8);
                }

                // Parse time part
                if (parts.length > 1) {
                    String timePart = parts[parts.length - 1].trim();
                    if (timePart.contains(":")) {
                        String[] timeParts = timePart.split("-");
                        if (timeParts.length >= 2) {
                            try {
                                String startTimeStr = timeParts[0].trim();
                                String endTimeStr = timeParts[1].trim();

                                if (startTimeStr.matches("\\d{1,2}:\\d{2}")) {
                                    schedule.setStartTime(LocalTime.parse(startTimeStr));
                                }
                                if (endTimeStr.matches("\\d{1,2}:\\d{2}")) {
                                    schedule.setEndTime(LocalTime.parse(endTimeStr));
                                }
                            } catch (Exception e) {
                                System.err.println("❌ Error parsing time: " + timePart);
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            System.err.println("❌ Error parsing schedule string: " + scheduleStr + " - " + e.getMessage());
        }
    }



    private void filterSchedule() {
        String searchText = searchField.getText().toLowerCase();
        String selectedDay = dayFilterComboBox.getValue();
        String selectedTime = timeFilterComboBox.getValue();

        List<CourseSchedule> filtered = allSchedules.stream()
                .filter(schedule -> {
                    boolean matchesSearch = searchText.isEmpty() ||
                            (schedule.getCourseName() != null && schedule.getCourseName().toLowerCase().contains(searchText)) ||
                            (schedule.getCourseId() != null && schedule.getCourseId().toLowerCase().contains(searchText)) ||
                            (schedule.getTeacherName() != null && schedule.getTeacherName().toLowerCase().contains(searchText)) ||
                            (schedule.getDayOfWeekText() != null && schedule.getDayOfWeekText().toLowerCase().contains(searchText)) ||
                            (schedule.getClassroom() != null && schedule.getClassroom().toLowerCase().contains(searchText));

                    boolean matchesDay = selectedDay == null || selectedDay.equals("Tất cả ngày") ||
                            schedule.getDayOfWeekText().equals(selectedDay);

                    boolean matchesTime = selectedTime == null || selectedTime.equals("Tất cả thời gian") ||
                            checkTimeMatch(schedule.getTimeRange(), selectedTime);

                    return matchesSearch && matchesDay && matchesTime;
                })
                .collect(Collectors.toList());

        filteredSchedules.setAll(filtered);
    }
    
    private boolean checkTimeMatch(String schedule, String timeFilter) {
        if (schedule == null) return false;
        
        switch (timeFilter) {
            case "Sáng (7:00-11:30)":
                return schedule.contains("7:") || schedule.contains("8:") || 
                       schedule.contains("9:") || schedule.contains("10:") || schedule.contains("11:");
            case "Chiều (13:00-17:30)":
                return schedule.contains("13:") || schedule.contains("14:") || 
                       schedule.contains("15:") || schedule.contains("16:") || schedule.contains("17:");
            case "Tối (18:00-21:30)":
                return schedule.contains("18:") || schedule.contains("19:") || 
                       schedule.contains("20:") || schedule.contains("21:");
            default:
                return true;
        }
    }
    
    private void updateStatistics() {
        if (currentStudent == null) return;

        // Count unique courses
        long uniqueCourses = allSchedules.stream()
                .map(CourseSchedule::getCourseId)
                .distinct()
                .count();
        totalCoursesLabel.setText(String.valueOf(uniqueCourses));

        // Calculate total credits from unique courses
        int totalCredits = allSchedules.stream()
                .map(CourseSchedule::getCourseId)
                .distinct()
                .mapToInt(courseId -> {
                    Course course = courseService.getCourseById(courseId);
                    return course != null ? course.getCredits() : 0;
                })
                .sum();
        totalCreditsLabel.setText(String.valueOf(totalCredits));

        // Set current semester (this could be dynamic based on current date)
        currentSemesterLabel.setText("Học kỳ 1 - 2024");
    }
    
    private void showScheduleDetails(CourseSchedule schedule) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Chi tiết lịch học");
        alert.setHeaderText(schedule.getCourseName() + " (" + schedule.getCourseId() + ")");

        StringBuilder content = new StringBuilder();

        // Get course information
        Course course = courseService.getCourseById(schedule.getCourseId());

        content.append("📚 Tên môn học: ").append(schedule.getCourseName()).append("\n");
        content.append("🆔 Mã môn học: ").append(schedule.getCourseId()).append("\n");
        content.append("📖 Mô tả: ").append(course != null && course.getDescription() != null ? course.getDescription() : "Không có mô tả").append("\n");
        content.append("🎯 Số tín chỉ: ").append(course != null ? course.getCredits() : "N/A").append("\n");
        content.append("👨‍🏫 Giáo viên: ").append(schedule.getTeacherName() != null ? schedule.getTeacherName() : "Chưa phân công").append("\n");

        content.append("\n📅 THÔNG TIN LỊCH HỌC:\n");
        content.append("═══════════════════════════════════\n");
        content.append("📆 Ngày: ").append(schedule.getDayOfWeekText()).append("\n");
        content.append("🕐 Giờ học: ").append(schedule.getTimeRange()).append("\n");
        content.append("🏫 Phòng học: ").append(schedule.getClassroom()).append("\n");
        content.append("📊 Loại tuần: ").append(schedule.getWeekTypeText()).append("\n");

        if (schedule.getSemester() != null) {
            content.append("🗓️ Học kỳ: ").append(schedule.getSemester());
            if (schedule.getAcademicYear() != null) {
                content.append(" - ").append(schedule.getAcademicYear());
            }
            content.append("\n");
        }

        content.append("✅ Trạng thái: ").append(schedule.isActive() ? "Đang hoạt động" : "Tạm dừng").append("\n");

        if (course != null) {
            content.append("\n👥 Thông tin lớp học:\n");
            content.append("Số sinh viên: ").append(
                course.getEnrolledStudents() != null ? course.getEnrolledStudents().size() : 0
            ).append("/").append(course.getMaxStudents()).append("\n");
        }

        TextArea textArea = new TextArea(content.toString());
        textArea.setEditable(false);
        textArea.setWrapText(true);
        textArea.setPrefSize(500, 400);

        alert.getDialogPane().setContent(textArea);
        alert.showAndWait();
    }
    
    @FXML
    private void goBack() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/student-dashboard.fxml", 
                                   "Sinh viên - " + SceneManager.getCurrentUser().getFullName());
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại trang chính: " + e.getMessage());
        }
    }
    
    @FXML
    private void refreshData() {
        System.out.println("🔄 Refreshing student schedule data...");
        loadSchedule();
        long uniqueCourses = allSchedules.stream()
                .map(CourseSchedule::getCourseId)
                .distinct()
                .count();
        showAlert("Thành công", "Dữ liệu lịch học đã được làm mới!\n\nĐã tải lại " + allSchedules.size() + " lịch học từ " + uniqueCourses + " môn học.");
    }
    
    @FXML
    private void exportSchedule() {
        if (filteredSchedules.isEmpty()) {
            showAlert("Thông báo", "Không có lịch học để xuất!");
            return;
        }

        StringBuilder report = new StringBuilder();
        report.append("LỊCH HỌC CÁ NHÂN\n");
        report.append("==================\n\n");
        report.append("Sinh viên: ").append(currentStudent.getFullName()).append("\n");
        report.append("MSSV: ").append(currentStudent.getStudentId()).append("\n");
        report.append("Lớp: ").append(currentStudent.getClassName()).append("\n");
        report.append("Ngày xuất: ").append(LocalDate.now()).append("\n\n");

        report.append("LỊCH HỌC CHI TIẾT:\n");
        report.append("===================\n");

        // Group schedules by course
        Map<String, List<CourseSchedule>> schedulesByCourse = filteredSchedules.stream()
                .collect(Collectors.groupingBy(CourseSchedule::getCourseId));

        for (Map.Entry<String, List<CourseSchedule>> entry : schedulesByCourse.entrySet()) {
            String courseId = entry.getKey();
            List<CourseSchedule> courseSchedules = entry.getValue();

            if (!courseSchedules.isEmpty()) {
                CourseSchedule firstSchedule = courseSchedules.get(0);
                Course course = courseService.getCourseById(courseId);

                report.append("📚 ").append(firstSchedule.getCourseName()).append(" (").append(courseId).append(")\n");
                report.append("   👨‍🏫 Giáo viên: ").append(firstSchedule.getTeacherName() != null ? firstSchedule.getTeacherName() : "Chưa phân công").append("\n");
                report.append("   🎯 Tín chỉ: ").append(course != null ? course.getCredits() : "N/A").append("\n");
                report.append("   📋 Lịch học:\n");

                for (CourseSchedule schedule : courseSchedules) {
                    report.append("      • ").append(schedule.getDayOfWeekText())
                           .append(" ").append(schedule.getTimeRange())
                           .append(" tại ").append(schedule.getClassroom());

                    if (!schedule.getWeekType().equals("ALL")) {
                        report.append(" (").append(schedule.getWeekTypeText()).append(")");
                    }

                    report.append("\n");
                }

                report.append("\n");
            }
        }

        // Calculate statistics
        long uniqueCourses = filteredSchedules.stream()
                .map(CourseSchedule::getCourseId)
                .distinct()
                .count();

        int totalCredits = filteredSchedules.stream()
                .map(CourseSchedule::getCourseId)
                .distinct()
                .mapToInt(courseId -> {
                    Course course = courseService.getCourseById(courseId);
                    return course != null ? course.getCredits() : 0;
                })
                .sum();

        report.append("TỔNG KẾT:\n");
        report.append("=========\n");
        report.append("Tổng số môn học: ").append(uniqueCourses).append("\n");
        report.append("Tổng số lịch học: ").append(filteredSchedules.size()).append("\n");
        report.append("Tổng số tín chỉ: ").append(totalCredits).append("\n");

        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Xuất lịch học");
        alert.setHeaderText("Lịch học cá nhân");

        TextArea textArea = new TextArea(report.toString());
        textArea.setEditable(false);
        textArea.setWrapText(true);
        textArea.setPrefSize(600, 500);

        alert.getDialogPane().setContent(textArea);
        alert.showAndWait();
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
